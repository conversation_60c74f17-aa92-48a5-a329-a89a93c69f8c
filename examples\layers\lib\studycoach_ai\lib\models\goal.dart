enum GoalType { shortTerm, longTerm }
enum GoalStatus { notStarted, inProgress, completed, paused }

class Goal {
  final String id;
  final String title;
  final String description;
  final GoalType type;
  final DateTime createdDate;
  final DateTime targetDate;
  final GoalStatus status;
  final int progress; // 0-100
  final List<String> milestones;
  final String? category;

  Goal({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.createdDate,
    required this.targetDate,
    this.status = GoalStatus.notStarted,
    this.progress = 0,
    this.milestones = const [],
    this.category,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString(),
      'createdDate': createdDate.toIso8601String(),
      'targetDate': targetDate.toIso8601String(),
      'status': status.toString(),
      'progress': progress,
      'milestones': milestones,
      'category': category,
    };
  }

  factory Goal.fromJson(Map<String, dynamic> json) {
    return Goal(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: GoalType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      createdDate: DateTime.parse(json['createdDate']),
      targetDate: DateTime.parse(json['targetDate']),
      status: GoalStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
      ),
      progress: json['progress'] ?? 0,
      milestones: List<String>.from(json['milestones'] ?? []),
      category: json['category'],
    );
  }

  Goal copyWith({
    String? id,
    String? title,
    String? description,
    GoalType? type,
    DateTime? createdDate,
    DateTime? targetDate,
    GoalStatus? status,
    int? progress,
    List<String>? milestones,
    String? category,
  }) {
    return Goal(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      createdDate: createdDate ?? this.createdDate,
      targetDate: targetDate ?? this.targetDate,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      milestones: milestones ?? this.milestones,
      category: category ?? this.category,
    );
  }
}
