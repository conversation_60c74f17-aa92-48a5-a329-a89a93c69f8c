import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/study_plan.dart';
import '../models/goal.dart';
import '../models/time_entry.dart';
import '../models/chat_message.dart';
import '../models/user.dart';

class StorageService {
  static const String _studyPlansKey = 'study_plans';
  static const String _goalsKey = 'goals';
  static const String _timeEntriesKey = 'time_entries';
  static const String _chatMessagesKey = 'chat_messages';
  static const String _userKey = 'user';

  // Study Plans
  Future<List<StudyPlan>> getStudyPlans() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_studyPlansKey);
      
      if (jsonString == null) return [];
      
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => StudyPlan.fromJson(json)).toList();
    } catch (e) {
      print('Error loading study plans: $e');
      return [];
    }
  }

  Future<void> saveStudyPlans(List<StudyPlan> studyPlans) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(studyPlans.map((plan) => plan.toJson()).toList());
      await prefs.setString(_studyPlansKey, jsonString);
    } catch (e) {
      print('Error saving study plans: $e');
    }
  }

  // Goals
  Future<List<Goal>> getGoals() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_goalsKey);
      
      if (jsonString == null) return [];
      
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => Goal.fromJson(json)).toList();
    } catch (e) {
      print('Error loading goals: $e');
      return [];
    }
  }

  Future<void> saveGoals(List<Goal> goals) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(goals.map((goal) => goal.toJson()).toList());
      await prefs.setString(_goalsKey, jsonString);
    } catch (e) {
      print('Error saving goals: $e');
    }
  }

  // Time Entries
  Future<List<TimeEntry>> getTimeEntries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_timeEntriesKey);
      
      if (jsonString == null) return [];
      
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => TimeEntry.fromJson(json)).toList();
    } catch (e) {
      print('Error loading time entries: $e');
      return [];
    }
  }

  Future<void> saveTimeEntries(List<TimeEntry> timeEntries) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(timeEntries.map((entry) => entry.toJson()).toList());
      await prefs.setString(_timeEntriesKey, jsonString);
    } catch (e) {
      print('Error saving time entries: $e');
    }
  }

  // Chat Messages
  Future<List<ChatMessage>> getChatMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_chatMessagesKey);
      
      if (jsonString == null) return [];
      
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => ChatMessage.fromJson(json)).toList();
    } catch (e) {
      print('Error loading chat messages: $e');
      return [];
    }
  }

  Future<void> saveChatMessages(List<ChatMessage> messages) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(messages.map((message) => message.toJson()).toList());
      await prefs.setString(_chatMessagesKey, jsonString);
    } catch (e) {
      print('Error saving chat messages: $e');
    }
  }

  // User
  Future<User?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_userKey);

      if (jsonString == null) return null;

      final Map<String, dynamic> json = jsonDecode(jsonString);
      return User.fromJson(json);
    } catch (e) {
      print('Error loading user: $e');
      return null;
    }
  }

  Future<void> saveUser(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(user.toJson());
      await prefs.setString(_userKey, jsonString);
    } catch (e) {
      print('Error saving user: $e');
    }
  }

  Future<void> clearUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
    } catch (e) {
      print('Error clearing user: $e');
    }
  }

  // Clear all data
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_studyPlansKey);
      await prefs.remove(_goalsKey);
      await prefs.remove(_timeEntriesKey);
      await prefs.remove(_chatMessagesKey);
      await prefs.remove(_userKey);
    } catch (e) {
      print('Error clearing data: $e');
    }
  }
}
