import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/curriculum.dart';
import '../models/weak_topics.dart';
import '../models/user.dart';
import '../providers/user_provider.dart';

class WeakTopicsSelectionScreen extends StatefulWidget {
  const WeakTopicsSelectionScreen({super.key});

  @override
  State<WeakTopicsSelectionScreen> createState() => _WeakTopicsSelectionScreenState();
}

class _WeakTopicsSelectionScreenState extends State<WeakTopicsSelectionScreen> {
  late List<CurriculumSubject> availableSubjects;
  Map<String, List<WeakTopic>> selectedWeakTopics = {};
  String? selectedSubjectId;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final user = userProvider.currentUser;
    
    if (user != null) {
      availableSubjects = CurriculumData.getSubjectsForLevel(user.educationLevel);
      
      // Mevcut eksik konuları yükle
      if (user.weakTopicsProfile != null) {
        for (final weakTopic in user.weakTopicsProfile!.getActiveWeakTopics()) {
          if (!selectedWeakTopics.containsKey(weakTopic.subjectId)) {
            selectedWeakTopics[weakTopic.subjectId] = [];
          }
          selectedWeakTopics[weakTopic.subjectId]!.add(weakTopic);
        }
      }
      
      // İlk dersi seç
      if (availableSubjects.isNotEmpty) {
        selectedSubjectId = availableSubjects.first.id;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Eksik Konularım'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveWeakTopics,
            child: const Text(
              'Kaydet',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: availableSubjects.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : Row(
              children: [
                // Sol taraf - Ders listesi
                Container(
                  width: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    border: Border(
                      right: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        ),
                        child: const Text(
                          'Dersler',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: availableSubjects.length,
                          itemBuilder: (context, index) {
                            final subject = availableSubjects[index];
                            final isSelected = selectedSubjectId == subject.id;
                            final weakTopicsCount = selectedWeakTopics[subject.id]?.length ?? 0;
                            
                            return ListTile(
                              title: Text(subject.name),
                              subtitle: weakTopicsCount > 0 
                                  ? Text('$weakTopicsCount eksik konu')
                                  : null,
                              selected: isSelected,
                              selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                              trailing: weakTopicsCount > 0
                                  ? Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).colorScheme.primary,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '$weakTopicsCount',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    )
                                  : null,
                              onTap: () {
                                setState(() {
                                  selectedSubjectId = subject.id;
                                });
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Sağ taraf - Konu listesi
                Expanded(
                  child: selectedSubjectId != null
                      ? _buildTopicsList()
                      : const Center(
                          child: Text('Bir ders seçin'),
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildTopicsList() {
    final subject = availableSubjects.firstWhere((s) => s.id == selectedSubjectId);
    final subjectWeakTopics = selectedWeakTopics[selectedSubjectId] ?? [];
    
    return Column(
      children: [
        // Başlık
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subject.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      subject.description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              if (subjectWeakTopics.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${subjectWeakTopics.length} eksik konu',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        // Konular listesi
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: subject.topics.length,
            itemBuilder: (context, index) {
              final topic = subject.topics[index];
              final existingWeakTopic = subjectWeakTopics
                  .where((wt) => wt.topicId == topic.id)
                  .firstOrNull;
              
              return _buildTopicCard(topic, existingWeakTopic);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTopicCard(CurriculumTopic topic, WeakTopic? existingWeakTopic) {
    final isSelected = existingWeakTopic != null;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: () => _showTopicSelectionDialog(topic, existingWeakTopic),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      topic.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isSelected 
                            ? Theme.of(context).colorScheme.primary
                            : null,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getWeaknessColor(existingWeakTopic!.weaknessLevel),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        existingWeakTopic.weaknessLevel.displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                topic.description,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildInfoChip(
                    Icons.schedule,
                    '${topic.estimatedHours} saat',
                    Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  _buildInfoChip(
                    Icons.trending_up,
                    'Zorluk: ${topic.difficulty}/5',
                    Colors.orange,
                  ),
                  if (topic.prerequisites.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    _buildInfoChip(
                      Icons.link,
                      '${topic.prerequisites.length} önkoşul',
                      Colors.purple,
                    ),
                  ],
                ],
              ),
              if (existingWeakTopic?.personalNote != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          existingWeakTopic!.personalNote!,
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getWeaknessColor(WeaknessLevel level) {
    switch (level) {
      case WeaknessLevel.slight:
        return Colors.orange;
      case WeaknessLevel.moderate:
        return Colors.deepOrange;
      case WeaknessLevel.severe:
        return Colors.red;
    }
  }

  void _showTopicSelectionDialog(CurriculumTopic topic, WeakTopic? existingWeakTopic) {
    WeaknessLevel selectedLevel = existingWeakTopic?.weaknessLevel ?? WeaknessLevel.moderate;
    final noteController = TextEditingController(text: existingWeakTopic?.personalNote ?? '');

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(topic.name),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  topic.description,
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),

                if (existingWeakTopic != null) ...[
                  Row(
                    children: [
                      Icon(Icons.remove_circle, color: Colors.red),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: () {
                          _removeWeakTopic(topic.id);
                          Navigator.pop(context);
                        },
                        child: const Text(
                          'Bu konuyu listeden çıkar',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                  const Divider(),
                ],

                const Text(
                  'Eksiklik Seviyesi:',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),

                ...WeaknessLevel.values.map((level) =>
                  RadioListTile<WeaknessLevel>(
                    title: Text(level.displayName),
                    subtitle: Text(level.description),
                    value: level,
                    groupValue: selectedLevel,
                    onChanged: (value) {
                      setDialogState(() {
                        selectedLevel = value!;
                      });
                    },
                  ),
                ),

                const SizedBox(height: 16),
                TextField(
                  controller: noteController,
                  decoration: const InputDecoration(
                    labelText: 'Kişisel Not (Opsiyonel)',
                    hintText: 'Bu konuyla ilgili notlarınız...',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('İptal'),
            ),
            ElevatedButton(
              onPressed: () {
                _addOrUpdateWeakTopic(topic, selectedLevel, noteController.text.trim());
                Navigator.pop(context);
              },
              child: Text(existingWeakTopic != null ? 'Güncelle' : 'Ekle'),
            ),
          ],
        ),
      ),
    );
  }

  void _addOrUpdateWeakTopic(CurriculumTopic topic, WeaknessLevel level, String note) {
    final weakTopic = WeakTopic(
      topicId: topic.id,
      subjectId: selectedSubjectId!,
      weaknessLevel: level,
      personalNote: note.isEmpty ? null : note,
    );

    setState(() {
      if (!selectedWeakTopics.containsKey(selectedSubjectId)) {
        selectedWeakTopics[selectedSubjectId!] = [];
      }

      // Mevcut konuyu kaldır
      selectedWeakTopics[selectedSubjectId!]!.removeWhere((wt) => wt.topicId == topic.id);

      // Yeni konuyu ekle
      selectedWeakTopics[selectedSubjectId!]!.add(weakTopic);
    });
  }

  void _removeWeakTopic(String topicId) {
    setState(() {
      selectedWeakTopics[selectedSubjectId]?.removeWhere((wt) => wt.topicId == topicId);
      if (selectedWeakTopics[selectedSubjectId]?.isEmpty == true) {
        selectedWeakTopics.remove(selectedSubjectId);
      }
    });
  }

  Future<void> _saveWeakTopics() async {
    setState(() {
      isLoading = true;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.currentUser;

      if (user != null) {
        // Tüm seçilen eksik konuları birleştir
        final allWeakTopics = <WeakTopic>[];
        for (final topics in selectedWeakTopics.values) {
          allWeakTopics.addAll(topics);
        }

        // Yeni profil oluştur
        final newProfile = WeakTopicsProfile(weakTopics: allWeakTopics);

        // Kullanıcıyı güncelle
        await userProvider.updateProfile(
          weakTopicsProfile: newProfile,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Eksik konularınız başarıyla kaydedildi!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hata: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
