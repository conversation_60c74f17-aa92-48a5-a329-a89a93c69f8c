import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/schedule_entry.dart';
import '../models/weak_topics.dart';
import '../models/mock_exam.dart';
import '../services/storage_service.dart';
import 'package:uuid/uuid.dart';

class UserProvider with ChangeNotifier {
  final StorageService _storageService = StorageService();
  final Uuid _uuid = const Uuid();
  
  User? _currentUser;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _currentUser != null;
  bool get hasCompletedOnboarding => _currentUser?.hasCompletedOnboarding ?? false;

  UserProvider() {
    loadUser();
  }

  Future<void> loadUser() async {
    _isLoading = true;
    notifyListeners();

    try {
      _currentUser = await _storageService.getUser();
    } catch (e) {
      debugPrint('Error loading user: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> createUser({
    required String name,
    required String email,
    required DateTime birthDate,
    required EducationLevel educationLevel,
    String? school,
    String? major,
    List<String>? subjects,
    required StudyStyle preferredStudyStyle,
    required int dailyStudyGoalMinutes,
    List<String>? studyDays,
    WeeklySchedule? weeklySchedule,
  }) async {
    try {
      final user = User(
        id: _uuid.v4(),
        name: name,
        email: email,
        birthDate: birthDate,
        educationLevel: educationLevel,
        school: school,
        major: major,
        subjects: subjects ?? [],
        preferredStudyStyle: preferredStudyStyle,
        dailyStudyGoalMinutes: dailyStudyGoalMinutes,
        studyDays: studyDays ?? [],
        weeklySchedule: weeklySchedule,
        createdAt: DateTime.now(),
        hasCompletedOnboarding: true,
      );

      _currentUser = user;
      await _storageService.saveUser(user);
      notifyListeners();
    } catch (e) {
      debugPrint('Error creating user: $e');
      rethrow;
    }
  }

  Future<void> updateUser(User updatedUser) async {
    try {
      _currentUser = updatedUser;
      await _storageService.saveUser(updatedUser);
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating user: $e');
      rethrow;
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    DateTime? birthDate,
    EducationLevel? educationLevel,
    String? school,
    String? major,
    List<String>? subjects,
    StudyStyle? preferredStudyStyle,
    int? dailyStudyGoalMinutes,
    List<String>? studyDays,
    WeeklySchedule? weeklySchedule,
    WeakTopicsProfile? weakTopicsProfile,
    MockExamProfile? mockExamProfile,
    String? profileImagePath,
  }) async {
    if (_currentUser == null) return;

    try {
      final updatedUser = _currentUser!.copyWith(
        name: name,
        email: email,
        birthDate: birthDate,
        educationLevel: educationLevel,
        school: school,
        major: major,
        subjects: subjects,
        preferredStudyStyle: preferredStudyStyle,
        dailyStudyGoalMinutes: dailyStudyGoalMinutes,
        studyDays: studyDays,
        weeklySchedule: weeklySchedule,
        weakTopicsProfile: weakTopicsProfile,
        mockExamProfile: mockExamProfile,
        profileImagePath: profileImagePath,
      );

      await updateUser(updatedUser);
    } catch (e) {
      debugPrint('Error updating profile: $e');
      rethrow;
    }
  }

  Future<void> completeOnboarding() async {
    if (_currentUser == null) return;

    try {
      final updatedUser = _currentUser!.copyWith(
        hasCompletedOnboarding: true,
      );

      await updateUser(updatedUser);
    } catch (e) {
      debugPrint('Error completing onboarding: $e');
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await _storageService.clearUser();
      _currentUser = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Error logging out: $e');
      rethrow;
    }
  }

  String getGreeting() {
    if (_currentUser == null) return 'Merhaba!';
    
    final hour = DateTime.now().hour;
    String timeGreeting;
    
    if (hour < 12) {
      timeGreeting = 'Günaydın';
    } else if (hour < 18) {
      timeGreeting = 'İyi öğleden sonralar';
    } else {
      timeGreeting = 'İyi akşamlar';
    }

    return '$timeGreeting, ${_currentUser!.name}!';
  }

  List<String> getPersonalizedTips() {
    if (_currentUser == null) return [];

    List<String> tips = [];

    // Çalışma stiline göre öneriler
    switch (_currentUser!.preferredStudyStyle) {
      case StudyStyle.visual:
        tips.addAll([
          'Renkli notlar ve diyagramlar kullan',
          'Mind map\'ler oluştur',
          'Görsel yardımcılar kullan',
        ]);
        break;
      case StudyStyle.auditory:
        tips.addAll([
          'Sesli okuma yap',
          'Müzik eşliğinde çalış',
          'Grup çalışmaları yap',
        ]);
        break;
      case StudyStyle.kinesthetic:
        tips.addAll([
          'Hareket ederek öğren',
          'Pratik uygulamalar yap',
          'Kısa molalar ver',
        ]);
        break;
      case StudyStyle.readingWriting:
        tips.addAll([
          'Çok not al',
          'Özetler hazırla',
          'Yazarak tekrar yap',
        ]);
        break;
    }

    // Eğitim seviyesine göre öneriler
    switch (_currentUser!.educationLevel) {
      case EducationLevel.grade5:
      case EducationLevel.grade6:
      case EducationLevel.grade7:
      case EducationLevel.grade8:
        tips.addAll([
          'Temel konuları sağlam öğren',
          'Düzenli tekrar yap',
          'Okuma alışkanlığı geliştir',
        ]);
        break;
      case EducationLevel.grade9:
      case EducationLevel.grade10:
        tips.addAll([
          'Lise hayatına uyum sağla',
          'Ders çalışma disiplini oluştur',
          'Alan seçimi için araştırma yap',
        ]);
        break;
      case EducationLevel.grade11:
      case EducationLevel.grade12:
        tips.addAll([
          'Üniversite sınavına hazırlan',
          'Hedef üniversite belirle',
          'Deneme sınavları düzenli çöz',
          'Zaman yönetimini geliştir',
        ]);
        break;
      case EducationLevel.graduate:
        tips.addAll([
          'YKS\'ye odaklanarak çalış',
          'Hedef üniversite ve bölüm belirle',
          'Deneme sınavlarını düzenli çöz',
          'Motivasyonunu yüksek tut',
          'Zaman yönetimini optimize et',
        ]);
        break;
    }

    return tips;
  }
}
