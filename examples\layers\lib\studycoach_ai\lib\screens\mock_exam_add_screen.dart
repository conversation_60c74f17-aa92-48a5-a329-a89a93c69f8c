import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/mock_exam.dart';
import '../models/user.dart';
import '../providers/user_provider.dart';

class MockExamAddScreen extends StatefulWidget {
  final MockExam? examToEdit;

  const MockExamAddScreen({super.key, this.examToEdit});

  @override
  State<MockExamAddScreen> createState() => _MockExamAddScreenState();
}

class _MockExamAddScreenState extends State<MockExamAddScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _examCenterController = TextEditingController();
  final _notesController = TextEditingController();
  final _rankingController = TextEditingController();
  final _totalParticipantsController = TextEditingController();

  ExamType? _selectedExamType;
  DateTime _selectedDate = DateTime.now();
  Map<String, TextEditingController> _correctControllers = {};
  Map<String, TextEditingController> _wrongControllers = {};
  Map<String, TextEditingController> _emptyControllers = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    if (widget.examToEdit != null) {
      _loadExamData();
    } else {
      // Yeni sınav için _selectedExamType null olarak kalacak
      _selectedExamType = null;
    }
  }

  void _loadExamData() {
    final exam = widget.examToEdit!;
    _nameController.text = exam.name;
    _selectedExamType = exam.examType;
    _selectedDate = exam.examDate;
    _examCenterController.text = exam.examCenter ?? '';
    _notesController.text = exam.notes ?? '';
    _rankingController.text = exam.ranking?.toString() ?? '';
    _totalParticipantsController.text = exam.totalParticipants?.toString() ?? '';

    _initializeControllers();

    // Subject results
    for (final result in exam.subjectResults) {
      _correctControllers[result.subjectName]?.text = result.correctAnswers.toString();
      _wrongControllers[result.subjectName]?.text = result.wrongAnswers.toString();
      _emptyControllers[result.subjectName]?.text = result.emptyAnswers.toString();
    }
  }

  void _initializeControllers() {
    if (_selectedExamType != null) {
      _correctControllers.clear();
      _wrongControllers.clear();
      _emptyControllers.clear();
      
      for (final subject in _selectedExamType!.subjects) {
        _correctControllers[subject] = TextEditingController();
        _wrongControllers[subject] = TextEditingController();
        _emptyControllers[subject] = TextEditingController();
      }
    }
  }

  void _onExamTypeChanged(ExamType? newType) {
    if (newType != null && newType != _selectedExamType) {
      setState(() {
        _selectedExamType = newType;
        _initializeControllers();
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _examCenterController.dispose();
    _notesController.dispose();
    _rankingController.dispose();
    _totalParticipantsController.dispose();
    
    for (final controller in _correctControllers.values) {
      controller.dispose();
    }
    for (final controller in _wrongControllers.values) {
      controller.dispose();
    }
    for (final controller in _emptyControllers.values) {
      controller.dispose();
    }
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.examToEdit != null ? 'Deneme Sınavını Düzenle' : 'Deneme Sınavı Ekle'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveExam,
            child: Text(
              'Kaydet',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Genel Bilgiler
              _buildSectionTitle('Genel Bilgiler'),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Sınav Adı',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.quiz),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Sınav adı gereklidir';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildExamTypeDropdown(),
              
              const SizedBox(height: 16),
              
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Sınav Tarihi',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    '${_selectedDate.day.toString().padLeft(2, '0')}/${_selectedDate.month.toString().padLeft(2, '0')}/${_selectedDate.year}',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _examCenterController,
                decoration: const InputDecoration(
                  labelText: 'Sınav Merkezi (Opsiyonel)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Sıralama Bilgileri
              _buildSectionTitle('Sıralama Bilgileri (Opsiyonel)'),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _rankingController,
                      decoration: const InputDecoration(
                        labelText: 'Sıralamanız',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.emoji_events),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _totalParticipantsController,
                      decoration: const InputDecoration(
                        labelText: 'Toplam Katılımcı',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.people),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Ders Sonuçları
              if (_selectedExamType != null) ...[
                _buildSectionTitle('Ders Sonuçları'),
                const SizedBox(height: 16),
                
                ..._selectedExamType!.subjects.map((subject) => 
                  _buildSubjectResultCard(subject),
                ),
              ],
              
              const SizedBox(height: 24),
              
              // Notlar
              _buildSectionTitle('Notlar (Opsiyonel)'),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Sınav hakkında notlarınız',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildExamTypeDropdown() {
    final availableTypes = _getAvailableExamTypes();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Sınav Türü'),
          const SizedBox(height: 8),
          ...availableTypes.map((type) =>
            ListTile(
              title: Text(type.displayName),
              leading: Radio<ExamType>(
                value: type,
                groupValue: _selectedExamType,
                onChanged: _onExamTypeChanged,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<ExamType> _getAvailableExamTypes() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final user = userProvider.currentUser;
    
    if (user == null) return [ExamType.custom];
    
    // Ortaokul öğrencileri için LGS
    if ([EducationLevel.grade5, EducationLevel.grade6, EducationLevel.grade7, EducationLevel.grade8].contains(user.educationLevel)) {
      return [ExamType.lgs, ExamType.custom];
    }
    
    // Lise öğrencileri ve mezunlar için YKS
    return [ExamType.tyt, ExamType.ayt, ExamType.msu, ExamType.custom];
  }

  Widget _buildSubjectResultCard(String subject) {
    final questionCount = _selectedExamType?.subjectQuestionCounts[subject] ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    subject,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$questionCount soru',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _correctControllers[subject],
                    decoration: const InputDecoration(
                      labelText: 'Doğru',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.check_circle, color: Colors.green),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) => _validateAnswer(value, questionCount),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _wrongControllers[subject],
                    decoration: const InputDecoration(
                      labelText: 'Yanlış',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.cancel, color: Colors.red),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) => _validateAnswer(value, questionCount),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _emptyControllers[subject],
                    decoration: const InputDecoration(
                      labelText: 'Boş',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.radio_button_unchecked, color: Colors.orange),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) => _validateAnswer(value, questionCount),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String? _validateAnswer(String? value, int maxQuestions) {
    if (value == null || value.isEmpty) {
      return null; // Boş bırakılabilir
    }

    final intValue = int.tryParse(value);
    if (intValue == null) {
      return 'Geçerli bir sayı girin';
    }

    if (intValue < 0) {
      return 'Negatif olamaz';
    }

    if (intValue > maxQuestions) {
      return 'En fazla $maxQuestions olabilir';
    }

    return null;
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('tr', 'TR'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveExam() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.currentUser;

      if (user == null) {
        throw Exception('Kullanıcı bulunamadı');
      }

      if (_selectedExamType == null) {
        throw Exception('Sınav türü seçilmedi');
      }

      // Subject results oluştur
      final subjectResults = <SubjectResult>[];
      for (final subject in _selectedExamType!.subjects) {
        final correct = int.tryParse(_correctControllers[subject]?.text ?? '0') ?? 0;
        final wrong = int.tryParse(_wrongControllers[subject]?.text ?? '0') ?? 0;
        final empty = int.tryParse(_emptyControllers[subject]?.text ?? '0') ?? 0;
        final totalQuestions = _selectedExamType!.subjectQuestionCounts[subject] ?? 0;

        // Toplam kontrol
        if (correct + wrong + empty > totalQuestions) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$subject için toplam cevap sayısı $totalQuestions\'ü geçemez'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        subjectResults.add(SubjectResult(
          subjectName: subject,
          correctAnswers: correct,
          wrongAnswers: wrong,
          emptyAnswers: empty,
          totalQuestions: totalQuestions,
        ));
      }

      // Mock exam oluştur
      final mockExam = MockExam(
        id: widget.examToEdit?.id ?? const Uuid().v4(),
        name: _nameController.text.trim(),
        examType: _selectedExamType!,
        examDate: _selectedDate,
        subjectResults: subjectResults,
        ranking: int.tryParse(_rankingController.text),
        totalParticipants: int.tryParse(_totalParticipantsController.text),
        examCenter: _examCenterController.text.trim().isEmpty ? null : _examCenterController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      // Profili güncelle
      final currentProfile = user.mockExamProfile ?? MockExamProfile();
      final updatedProfile = widget.examToEdit != null
          ? currentProfile.updateExam(mockExam)
          : currentProfile.addExam(mockExam);

      await userProvider.updateProfile(mockExamProfile: updatedProfile);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.examToEdit != null
                ? 'Deneme sınavı başarıyla güncellendi!'
                : 'Deneme sınavı başarıyla eklendi!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hata: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
