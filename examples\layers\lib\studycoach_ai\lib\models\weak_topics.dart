import 'curriculum.dart';

enum WeaknessLevel {
  slight,    // <PERSON><PERSON><PERSON> e<PERSON>
  moderate,  // <PERSON>ta d<PERSON> e<PERSON>
  severe,    // <PERSON><PERSON><PERSON> e<PERSON>
}

extension WeaknessLevelExtension on WeaknessLevel {
  String get displayName {
    switch (this) {
      case WeaknessLevel.slight:
        return '<PERSON><PERSON><PERSON>';
      case WeaknessLevel.moderate:
        return '<PERSON><PERSON> Düzey Eksiklik';
      case WeaknessLevel.severe:
        return '<PERSON><PERSON><PERSON>';
    }
  }

  String get description {
    switch (this) {
      case WeaknessLevel.slight:
        return 'Bu konuda küçük boşluklarım var';
      case WeaknessLevel.moderate:
        return 'Bu konuyu tekrar etmem gerekiyor';
      case WeaknessLevel.severe:
        return 'Bu konuyu baştan öğrenmem gerekiyor';
    }
  }

  int get priorityScore {
    switch (this) {
      case WeaknessLevel.slight:
        return 1;
      case WeaknessLevel.moderate:
        return 2;
      case WeaknessLevel.severe:
        return 3;
    }
  }

  int get estimatedExtraHours {
    switch (this) {
      case WeaknessLevel.slight:
        return 1;
      case WeaknessLevel.moderate:
        return 2;
      case WeaknessLevel.severe:
        return 4;
    }
  }
}

class WeakTopic {
  final String topicId;
  final String subjectId;
  final WeaknessLevel weaknessLevel;
  final String? personalNote;
  final DateTime addedDate;
  final bool isActive;

  WeakTopic({
    required this.topicId,
    required this.subjectId,
    required this.weaknessLevel,
    this.personalNote,
    DateTime? addedDate,
    this.isActive = true,
  }) : addedDate = addedDate ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'topicId': topicId,
      'subjectId': subjectId,
      'weaknessLevel': weaknessLevel.toString(),
      'personalNote': personalNote,
      'addedDate': addedDate.toIso8601String(),
      'isActive': isActive,
    };
  }

  factory WeakTopic.fromJson(Map<String, dynamic> json) {
    return WeakTopic(
      topicId: json['topicId'],
      subjectId: json['subjectId'],
      weaknessLevel: WeaknessLevel.values.firstWhere(
        (e) => e.toString() == json['weaknessLevel'],
      ),
      personalNote: json['personalNote'],
      addedDate: DateTime.parse(json['addedDate']),
      isActive: json['isActive'] ?? true,
    );
  }

  WeakTopic copyWith({
    String? topicId,
    String? subjectId,
    WeaknessLevel? weaknessLevel,
    String? personalNote,
    DateTime? addedDate,
    bool? isActive,
  }) {
    return WeakTopic(
      topicId: topicId ?? this.topicId,
      subjectId: subjectId ?? this.subjectId,
      weaknessLevel: weaknessLevel ?? this.weaknessLevel,
      personalNote: personalNote ?? this.personalNote,
      addedDate: addedDate ?? this.addedDate,
      isActive: isActive ?? this.isActive,
    );
  }

  // Toplam tahmini çalışma saatini hesapla
  int calculateEstimatedHours(CurriculumTopic topic) {
    return topic.estimatedHours + weaknessLevel.estimatedExtraHours;
  }

  // Öncelik puanını hesapla (yüksek puan = yüksek öncelik)
  int calculatePriority(CurriculumTopic topic) {
    int baseScore = weaknessLevel.priorityScore * 10;
    int difficultyBonus = topic.difficulty * 2;
    int prerequisiteBonus = topic.prerequisites.length * 5;
    
    return baseScore + difficultyBonus + prerequisiteBonus;
  }
}

class WeakTopicsProfile {
  final List<WeakTopic> weakTopics;
  final DateTime lastUpdated;

  WeakTopicsProfile({
    this.weakTopics = const [],
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'weakTopics': weakTopics.map((t) => t.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory WeakTopicsProfile.fromJson(Map<String, dynamic> json) {
    return WeakTopicsProfile(
      weakTopics: (json['weakTopics'] as List?)
          ?.map((t) => WeakTopic.fromJson(t))
          .toList() ?? [],
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'])
          : DateTime.now(),
    );
  }

  // Aktif eksik konuları getir
  List<WeakTopic> getActiveWeakTopics() {
    return weakTopics.where((topic) => topic.isActive).toList();
  }

  // Belirli bir ders için eksik konuları getir
  List<WeakTopic> getWeakTopicsForSubject(String subjectId) {
    return weakTopics
        .where((topic) => topic.subjectId == subjectId && topic.isActive)
        .toList();
  }

  // Öncelik sırasına göre sıralanmış eksik konuları getir
  List<WeakTopic> getTopicsByPriority(List<CurriculumTopic> allTopics) {
    final activeTopics = getActiveWeakTopics();
    
    activeTopics.sort((a, b) {
      final topicA = allTopics.firstWhere((t) => t.id == a.topicId);
      final topicB = allTopics.firstWhere((t) => t.id == b.topicId);
      
      return b.calculatePriority(topicB).compareTo(a.calculatePriority(topicA));
    });
    
    return activeTopics;
  }

  // Toplam tahmini çalışma saatini hesapla
  int calculateTotalEstimatedHours(List<CurriculumTopic> allTopics) {
    int totalHours = 0;
    
    for (final weakTopic in getActiveWeakTopics()) {
      final topic = allTopics.firstWhere((t) => t.id == weakTopic.topicId);
      totalHours += weakTopic.calculateEstimatedHours(topic);
    }
    
    return totalHours;
  }

  // Eksiklik seviyesine göre istatistik
  Map<WeaknessLevel, int> getWeaknessStatistics() {
    final stats = <WeaknessLevel, int>{
      WeaknessLevel.slight: 0,
      WeaknessLevel.moderate: 0,
      WeaknessLevel.severe: 0,
    };
    
    for (final topic in getActiveWeakTopics()) {
      stats[topic.weaknessLevel] = (stats[topic.weaknessLevel] ?? 0) + 1;
    }
    
    return stats;
  }

  // Yeni eksik konu ekle
  WeakTopicsProfile addWeakTopic(WeakTopic newTopic) {
    final updatedTopics = List<WeakTopic>.from(weakTopics);
    
    // Aynı konu zaten varsa güncelle
    final existingIndex = updatedTopics.indexWhere(
      (topic) => topic.topicId == newTopic.topicId,
    );
    
    if (existingIndex != -1) {
      updatedTopics[existingIndex] = newTopic;
    } else {
      updatedTopics.add(newTopic);
    }
    
    return WeakTopicsProfile(
      weakTopics: updatedTopics,
      lastUpdated: DateTime.now(),
    );
  }

  // Eksik konuyu kaldır
  WeakTopicsProfile removeWeakTopic(String topicId) {
    final updatedTopics = weakTopics
        .where((topic) => topic.topicId != topicId)
        .toList();
    
    return WeakTopicsProfile(
      weakTopics: updatedTopics,
      lastUpdated: DateTime.now(),
    );
  }

  // Eksik konuyu pasif yap (tamamlandı olarak işaretle)
  WeakTopicsProfile markTopicAsCompleted(String topicId) {
    final updatedTopics = weakTopics.map((topic) {
      if (topic.topicId == topicId) {
        return topic.copyWith(isActive: false);
      }
      return topic;
    }).toList();
    
    return WeakTopicsProfile(
      weakTopics: updatedTopics,
      lastUpdated: DateTime.now(),
    );
  }

  WeakTopicsProfile copyWith({
    List<WeakTopic>? weakTopics,
    DateTime? lastUpdated,
  }) {
    return WeakTopicsProfile(
      weakTopics: weakTopics ?? this.weakTopics,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
