import 'curriculum.dart';

enum ProgressStatus {
  notStarted,
  inProgress,
  completed,
}

extension ProgressStatusExtension on ProgressStatus {
  String get displayName {
    switch (this) {
      case ProgressStatus.notStarted:
        return '<PERSON><PERSON><PERSON><PERSON><PERSON>';
      case ProgressStatus.inProgress:
        return 'Devam Ediyor';
      case ProgressStatus.completed:
        return 'Tam<PERSON>landı';
    }
  }

  String get description {
    switch (this) {
      case ProgressStatus.notStarted:
        return 'Bu konuya hen<PERSON> ba<PERSON>madı';
      case ProgressStatus.inProgress:
        return 'Bu konu üzerinde çalışılıyor';
      case ProgressStatus.completed:
        return 'Bu konu tamamlandı';
    }
  }
}

class TopicProgress {
  final ProgressStatus status;
  final int completedSubtopics;
  final int totalSubtopics;
  final List<String> completedActivities;
  final DateTime? lastStudiedDate;
  final int totalStudyMinutes;
  final List<ProgressEntry> progressEntries;

  TopicProgress({
    this.status = ProgressStatus.notStarted,
    this.completedSubtopics = 0,
    this.totalSubtopics = 0,
    this.completedActivities = const [],
    this.lastStudiedDate,
    this.totalStudyMinutes = 0,
    this.progressEntries = const [],
  });

  double get completionPercentage {
    if (totalSubtopics == 0) return 0.0;
    return (completedSubtopics / totalSubtopics) * 100;
  }

  bool get isCompleted => status == ProgressStatus.completed;

  Map<String, dynamic> toJson() {
    return {
      'status': status.toString(),
      'completedSubtopics': completedSubtopics,
      'totalSubtopics': totalSubtopics,
      'completedActivities': completedActivities,
      'lastStudiedDate': lastStudiedDate?.toIso8601String(),
      'totalStudyMinutes': totalStudyMinutes,
      'progressEntries': progressEntries.map((e) => e.toJson()).toList(),
    };
  }

  factory TopicProgress.fromJson(Map<String, dynamic> json) {
    return TopicProgress(
      status: ProgressStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => ProgressStatus.notStarted,
      ),
      completedSubtopics: json['completedSubtopics'] ?? 0,
      totalSubtopics: json['totalSubtopics'] ?? 0,
      completedActivities: List<String>.from(json['completedActivities'] ?? []),
      lastStudiedDate: json['lastStudiedDate'] != null
          ? DateTime.parse(json['lastStudiedDate'])
          : null,
      totalStudyMinutes: json['totalStudyMinutes'] ?? 0,
      progressEntries: (json['progressEntries'] as List<dynamic>?)
          ?.map((e) => ProgressEntry.fromJson(e))
          .toList() ?? [],
    );
  }

  TopicProgress copyWith({
    ProgressStatus? status,
    int? completedSubtopics,
    int? totalSubtopics,
    List<String>? completedActivities,
    DateTime? lastStudiedDate,
    int? totalStudyMinutes,
    List<ProgressEntry>? progressEntries,
  }) {
    return TopicProgress(
      status: status ?? this.status,
      completedSubtopics: completedSubtopics ?? this.completedSubtopics,
      totalSubtopics: totalSubtopics ?? this.totalSubtopics,
      completedActivities: completedActivities ?? this.completedActivities,
      lastStudiedDate: lastStudiedDate ?? this.lastStudiedDate,
      totalStudyMinutes: totalStudyMinutes ?? this.totalStudyMinutes,
      progressEntries: progressEntries ?? this.progressEntries,
    );
  }
}

class ProgressEntry {
  final DateTime date;
  final int studyMinutes;
  final String? note;
  final double progressBefore;
  final double progressAfter;

  ProgressEntry({
    required this.date,
    required this.studyMinutes,
    this.note,
    required this.progressBefore,
    required this.progressAfter,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'studyMinutes': studyMinutes,
      'note': note,
      'progressBefore': progressBefore,
      'progressAfter': progressAfter,
    };
  }

  factory ProgressEntry.fromJson(Map<String, dynamic> json) {
    return ProgressEntry(
      date: DateTime.parse(json['date']),
      studyMinutes: json['studyMinutes'],
      note: json['note'],
      progressBefore: json['progressBefore']?.toDouble() ?? 0.0,
      progressAfter: json['progressAfter']?.toDouble() ?? 0.0,
    );
  }
}

enum WeaknessLevel {
  slight,    // Hafif eksiklik
  moderate,  // Orta düzey eksiklik
  severe,    // Ciddi eksiklik
}

extension WeaknessLevelExtension on WeaknessLevel {
  String get displayName {
    switch (this) {
      case WeaknessLevel.slight:
        return 'Hafif Eksiklik';
      case WeaknessLevel.moderate:
        return 'Orta Düzey Eksiklik';
      case WeaknessLevel.severe:
        return 'Ciddi Eksiklik';
    }
  }

  String get description {
    switch (this) {
      case WeaknessLevel.slight:
        return 'Bu konuda küçük boşluklarım var';
      case WeaknessLevel.moderate:
        return 'Bu konuyu tekrar etmem gerekiyor';
      case WeaknessLevel.severe:
        return 'Bu konuyu baştan öğrenmem gerekiyor';
    }
  }

  int get priorityScore {
    switch (this) {
      case WeaknessLevel.slight:
        return 1;
      case WeaknessLevel.moderate:
        return 2;
      case WeaknessLevel.severe:
        return 3;
    }
  }

  int get estimatedExtraHours {
    switch (this) {
      case WeaknessLevel.slight:
        return 1;
      case WeaknessLevel.moderate:
        return 2;
      case WeaknessLevel.severe:
        return 4;
    }
  }
}

class WeakTopic {
  final String topicId;
  final String subjectId;
  final WeaknessLevel weaknessLevel;
  final String? personalNote;
  final DateTime addedDate;
  final bool isActive;
  final TopicProgress progress;

  WeakTopic({
    required this.topicId,
    required this.subjectId,
    required this.weaknessLevel,
    this.personalNote,
    DateTime? addedDate,
    this.isActive = true,
    TopicProgress? progress,
  }) : addedDate = addedDate ?? DateTime.now(),
       progress = progress ?? TopicProgress();

  Map<String, dynamic> toJson() {
    return {
      'topicId': topicId,
      'subjectId': subjectId,
      'weaknessLevel': weaknessLevel.toString(),
      'personalNote': personalNote,
      'addedDate': addedDate.toIso8601String(),
      'isActive': isActive,
      'progress': progress.toJson(),
    };
  }

  factory WeakTopic.fromJson(Map<String, dynamic> json) {
    return WeakTopic(
      topicId: json['topicId'],
      subjectId: json['subjectId'],
      weaknessLevel: WeaknessLevel.values.firstWhere(
        (e) => e.toString() == json['weaknessLevel'],
      ),
      personalNote: json['personalNote'],
      addedDate: DateTime.parse(json['addedDate']),
      isActive: json['isActive'] ?? true,
      progress: json['progress'] != null
          ? TopicProgress.fromJson(json['progress'])
          : TopicProgress(),
    );
  }

  WeakTopic copyWith({
    String? topicId,
    String? subjectId,
    WeaknessLevel? weaknessLevel,
    String? personalNote,
    DateTime? addedDate,
    bool? isActive,
    TopicProgress? progress,
  }) {
    return WeakTopic(
      topicId: topicId ?? this.topicId,
      subjectId: subjectId ?? this.subjectId,
      weaknessLevel: weaknessLevel ?? this.weaknessLevel,
      personalNote: personalNote ?? this.personalNote,
      addedDate: addedDate ?? this.addedDate,
      isActive: isActive ?? this.isActive,
      progress: progress ?? this.progress,
    );
  }

  // Toplam tahmini çalışma saatini hesapla
  int calculateEstimatedHours(CurriculumTopic topic) {
    return topic.estimatedHours + weaknessLevel.estimatedExtraHours;
  }

  // Öncelik puanını hesapla (yüksek puan = yüksek öncelik)
  int calculatePriority(CurriculumTopic topic) {
    int baseScore = weaknessLevel.priorityScore * 10;
    int difficultyBonus = topic.difficulty * 2;
    int prerequisiteBonus = topic.prerequisites.length * 5;
    
    return baseScore + difficultyBonus + prerequisiteBonus;
  }
}

class WeakTopicsProfile {
  final List<WeakTopic> weakTopics;
  final DateTime lastUpdated;

  WeakTopicsProfile({
    this.weakTopics = const [],
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'weakTopics': weakTopics.map((t) => t.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory WeakTopicsProfile.fromJson(Map<String, dynamic> json) {
    return WeakTopicsProfile(
      weakTopics: (json['weakTopics'] as List?)
          ?.map((t) => WeakTopic.fromJson(t))
          .toList() ?? [],
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'])
          : DateTime.now(),
    );
  }

  // Aktif eksik konuları getir
  List<WeakTopic> getActiveWeakTopics() {
    return weakTopics.where((topic) => topic.isActive).toList();
  }

  // Belirli bir ders için eksik konuları getir
  List<WeakTopic> getWeakTopicsForSubject(String subjectId) {
    return weakTopics
        .where((topic) => topic.subjectId == subjectId && topic.isActive)
        .toList();
  }

  // Öncelik sırasına göre sıralanmış eksik konuları getir
  List<WeakTopic> getTopicsByPriority(List<CurriculumTopic> allTopics) {
    final activeTopics = getActiveWeakTopics();
    
    activeTopics.sort((a, b) {
      final topicA = allTopics.firstWhere((t) => t.id == a.topicId);
      final topicB = allTopics.firstWhere((t) => t.id == b.topicId);
      
      return b.calculatePriority(topicB).compareTo(a.calculatePriority(topicA));
    });
    
    return activeTopics;
  }

  // Toplam tahmini çalışma saatini hesapla
  int calculateTotalEstimatedHours(List<CurriculumTopic> allTopics) {
    int totalHours = 0;
    
    for (final weakTopic in getActiveWeakTopics()) {
      final topic = allTopics.firstWhere((t) => t.id == weakTopic.topicId);
      totalHours += weakTopic.calculateEstimatedHours(topic);
    }
    
    return totalHours;
  }

  // Eksiklik seviyesine göre istatistik
  Map<WeaknessLevel, int> getWeaknessStatistics() {
    final stats = <WeaknessLevel, int>{
      WeaknessLevel.slight: 0,
      WeaknessLevel.moderate: 0,
      WeaknessLevel.severe: 0,
    };
    
    for (final topic in getActiveWeakTopics()) {
      stats[topic.weaknessLevel] = (stats[topic.weaknessLevel] ?? 0) + 1;
    }
    
    return stats;
  }

  // Yeni eksik konu ekle
  WeakTopicsProfile addWeakTopic(WeakTopic newTopic) {
    final updatedTopics = List<WeakTopic>.from(weakTopics);
    
    // Aynı konu zaten varsa güncelle
    final existingIndex = updatedTopics.indexWhere(
      (topic) => topic.topicId == newTopic.topicId,
    );
    
    if (existingIndex != -1) {
      updatedTopics[existingIndex] = newTopic;
    } else {
      updatedTopics.add(newTopic);
    }
    
    return WeakTopicsProfile(
      weakTopics: updatedTopics,
      lastUpdated: DateTime.now(),
    );
  }

  // Eksik konuyu kaldır
  WeakTopicsProfile removeWeakTopic(String topicId) {
    final updatedTopics = weakTopics
        .where((topic) => topic.topicId != topicId)
        .toList();
    
    return WeakTopicsProfile(
      weakTopics: updatedTopics,
      lastUpdated: DateTime.now(),
    );
  }

  // Eksik konuyu pasif yap (tamamlandı olarak işaretle)
  WeakTopicsProfile markTopicAsCompleted(String topicId) {
    final updatedTopics = weakTopics.map((topic) {
      if (topic.topicId == topicId) {
        return topic.copyWith(isActive: false);
      }
      return topic;
    }).toList();
    
    return WeakTopicsProfile(
      weakTopics: updatedTopics,
      lastUpdated: DateTime.now(),
    );
  }

  WeakTopicsProfile copyWith({
    List<WeakTopic>? weakTopics,
    DateTime? lastUpdated,
  }) {
    return WeakTopicsProfile(
      weakTopics: weakTopics ?? this.weakTopics,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
