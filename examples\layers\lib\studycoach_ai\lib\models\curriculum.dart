import 'user.dart';

class CurriculumTopic {
  final String id;
  final String name;
  final String description;
  final int difficulty; // 1-5 arası zorluk seviyesi
  final List<String> prerequisites; // Önkoşul konu ID'leri
  final int estimatedHours; // <PERSON><PERSON>ini çalışma saati

  CurriculumTopic({
    required this.id,
    required this.name,
    required this.description,
    this.difficulty = 1,
    this.prerequisites = const [],
    this.estimatedHours = 2,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'difficulty': difficulty,
      'prerequisites': prerequisites,
      'estimatedHours': estimatedHours,
    };
  }

  factory CurriculumTopic.fromJson(Map<String, dynamic> json) {
    return CurriculumTopic(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      difficulty: json['difficulty'] ?? 1,
      prerequisites: List<String>.from(json['prerequisites'] ?? []),
      estimatedHours: json['estimatedHours'] ?? 2,
    );
  }
}

class CurriculumSubject {
  final String id;
  final String name;
  final String description;
  final List<CurriculumTopic> topics;
  final List<EducationLevel> applicableLevels;

  CurriculumSubject({
    required this.id,
    required this.name,
    required this.description,
    required this.topics,
    required this.applicableLevels,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'topics': topics.map((t) => t.toJson()).toList(),
      'applicableLevels': applicableLevels.map((l) => l.toString()).toList(),
    };
  }

  factory CurriculumSubject.fromJson(Map<String, dynamic> json) {
    return CurriculumSubject(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      topics: (json['topics'] as List?)
          ?.map((t) => CurriculumTopic.fromJson(t))
          .toList() ?? [],
      applicableLevels: (json['applicableLevels'] as List?)
          ?.map((l) => EducationLevel.values.firstWhere(
                (e) => e.toString() == l,
              ))
          .toList() ?? [],
    );
  }
}

class CurriculumData {
  static List<CurriculumSubject> getAllSubjects() {
    return [
      // Matematik (Tüm seviyeler)
      CurriculumSubject(
        id: 'math',
        name: 'Matematik',
        description: 'Matematik dersi konuları',
        applicableLevels: EducationLevel.values,
        topics: _getMathTopics(),
      ),

      // Türkçe (Ortaokul) / Türk Dili ve Edebiyatı (Lise)
      CurriculumSubject(
        id: 'turkish',
        name: 'Türkçe',
        description: 'Türkçe dersi konuları',
        applicableLevels: [
          EducationLevel.grade5,
          EducationLevel.grade6,
          EducationLevel.grade7,
          EducationLevel.grade8,
        ],
        topics: _getTurkishTopics(),
      ),

      CurriculumSubject(
        id: 'turkish_literature',
        name: 'Türk Dili ve Edebiyatı',
        description: 'Türk Dili ve Edebiyatı dersi konuları',
        applicableLevels: [
          EducationLevel.grade9,
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.grade12,
          EducationLevel.graduate,
        ],
        topics: _getTurkishLiteratureTopics(),
      ),

      // Fen Bilimleri (Ortaokul)
      CurriculumSubject(
        id: 'science',
        name: 'Fen Bilimleri',
        description: 'Fen Bilimleri dersi konuları',
        applicableLevels: [
          EducationLevel.grade5,
          EducationLevel.grade6,
          EducationLevel.grade7,
          EducationLevel.grade8,
        ],
        topics: _getScienceTopics(),
      ),

      // Sosyal Bilgiler (Ortaokul)
      CurriculumSubject(
        id: 'social_studies',
        name: 'Sosyal Bilgiler',
        description: 'Sosyal Bilgiler dersi konuları',
        applicableLevels: [
          EducationLevel.grade5,
          EducationLevel.grade6,
          EducationLevel.grade7,
        ],
        topics: _getSocialStudiesTopics(),
      ),

      // T.C. İnkılap Tarihi ve Atatürkçülük (8. sınıf ve 12. sınıf)
      CurriculumSubject(
        id: 'revolution_history',
        name: 'T.C. İnkılap Tarihi ve Atatürkçülük',
        description: 'T.C. İnkılap Tarihi ve Atatürkçülük dersi konuları',
        applicableLevels: [
          EducationLevel.grade8,
          EducationLevel.grade12,
          EducationLevel.graduate,
        ],
        topics: _getRevolutionHistoryTopics(),
      ),

      // İngilizce (Tüm seviyeler)
      CurriculumSubject(
        id: 'english',
        name: 'İngilizce',
        description: 'İngilizce dersi konuları',
        applicableLevels: EducationLevel.values,
        topics: _getEnglishTopics(),
      ),

      // Fizik (Lise)
      CurriculumSubject(
        id: 'physics',
        name: 'Fizik',
        description: 'Fizik dersi konuları',
        applicableLevels: [
          EducationLevel.grade9,
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.grade12,
          EducationLevel.graduate,
        ],
        topics: _getPhysicsTopics(),
      ),

      // Kimya (Lise)
      CurriculumSubject(
        id: 'chemistry',
        name: 'Kimya',
        description: 'Kimya dersi konuları',
        applicableLevels: [
          EducationLevel.grade9,
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.grade12,
          EducationLevel.graduate,
        ],
        topics: _getChemistryTopics(),
      ),

      // Biyoloji (Lise)
      CurriculumSubject(
        id: 'biology',
        name: 'Biyoloji',
        description: 'Biyoloji dersi konuları',
        applicableLevels: [
          EducationLevel.grade9,
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.grade12,
          EducationLevel.graduate,
        ],
        topics: _getBiologyTopics(),
      ),

      // Tarih (Lise)
      CurriculumSubject(
        id: 'history',
        name: 'Tarih',
        description: 'Tarih dersi konuları',
        applicableLevels: [
          EducationLevel.grade9,
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.graduate,
        ],
        topics: _getHistoryTopics(),
      ),

      // Coğrafya (Lise)
      CurriculumSubject(
        id: 'geography',
        name: 'Coğrafya',
        description: 'Coğrafya dersi konuları',
        applicableLevels: [
          EducationLevel.grade9,
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.grade12,
          EducationLevel.graduate,
        ],
        topics: _getGeographyTopics(),
      ),

      // Felsefe (Lise)
      CurriculumSubject(
        id: 'philosophy',
        name: 'Felsefe',
        description: 'Felsefe dersi konuları',
        applicableLevels: [
          EducationLevel.grade10,
          EducationLevel.grade11,
          EducationLevel.graduate,
        ],
        topics: _getPhilosophyTopics(),
      ),
    ];
  }

  static List<CurriculumSubject> getSubjectsForLevel(EducationLevel level) {
    return getAllSubjects()
        .where((subject) => subject.applicableLevels.contains(level))
        .map((subject) => _filterTopicsForLevel(subject, level))
        .toList();
  }

  static CurriculumSubject _filterTopicsForLevel(CurriculumSubject subject, EducationLevel level) {
    List<CurriculumTopic> filteredTopics = [];

    // Sınıf seviyesine göre konuları filtrele
    switch (subject.id) {
      case 'math':
        filteredTopics = _getMathTopicsForLevel(level);
        break;
      case 'turkish':
        if ([EducationLevel.grade5, EducationLevel.grade6, EducationLevel.grade7, EducationLevel.grade8].contains(level)) {
          filteredTopics = _getTurkishTopics();
        }
        break;
      case 'turkish_literature':
        if ([EducationLevel.grade9, EducationLevel.grade10, EducationLevel.grade11, EducationLevel.grade12, EducationLevel.graduate].contains(level)) {
          filteredTopics = _getTurkishLiteratureTopics();
        }
        break;
      case 'science':
        filteredTopics = _getScienceTopicsForLevel(level);
        break;
      case 'social_studies':
        filteredTopics = _getSocialStudiesTopicsForLevel(level);
        break;
      case 'revolution_history':
        filteredTopics = _getRevolutionHistoryTopics();
        break;
      case 'english':
        filteredTopics = _getEnglishTopicsForLevel(level);
        break;
      case 'physics':
        filteredTopics = _getPhysicsTopicsForLevel(level);
        break;
      case 'chemistry':
        filteredTopics = _getChemistryTopicsForLevel(level);
        break;
      case 'biology':
        filteredTopics = _getBiologyTopicsForLevel(level);
        break;
      case 'history':
        filteredTopics = _getHistoryTopicsForLevel(level);
        break;
      case 'geography':
        filteredTopics = _getGeographyTopicsForLevel(level);
        break;
      case 'philosophy':
        filteredTopics = _getPhilosophyTopics();
        break;
      default:
        filteredTopics = subject.topics;
    }

    return CurriculumSubject(
      id: subject.id,
      name: subject.name,
      description: subject.description,
      applicableLevels: subject.applicableLevels,
      topics: filteredTopics,
    );
  }

  static List<CurriculumTopic> _getMathTopics() {
    return [
      // 5. Sınıf Matematik
      CurriculumTopic(
        id: 'math_natural_numbers',
        name: 'Doğal Sayılar',
        description: 'Doğal sayılar ve işlemler',
        difficulty: 1,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_fractions',
        name: 'Kesirler',
        description: 'Kesir kavramı ve işlemleri',
        difficulty: 2,
        prerequisites: ['math_natural_numbers'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_decimal_numbers',
        name: 'Ondalık Gösterim',
        description: 'Ondalık sayılar ve işlemler',
        difficulty: 2,
        prerequisites: ['math_fractions'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_percentage',
        name: 'Yüzdeler',
        description: 'Yüzde hesaplamaları',
        difficulty: 2,
        prerequisites: ['math_decimal_numbers'],
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'math_basic_geometry',
        name: 'Temel Geometrik Kavramlar',
        description: 'Nokta, doğru, düzlem, açı',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_triangles',
        name: 'Üçgenler',
        description: 'Üçgen türleri ve özellikleri',
        difficulty: 3,
        prerequisites: ['math_basic_geometry'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_area_perimeter',
        name: 'Alan ve Çevre',
        description: 'Geometrik şekillerin alan ve çevre hesaplamaları',
        difficulty: 3,
        prerequisites: ['math_triangles'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_integers',
        name: 'Tam Sayılar',
        description: 'Tam sayılar ve işlemler',
        difficulty: 3,
        prerequisites: ['math_natural_numbers'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_rational_numbers',
        name: 'Rasyonel Sayılar',
        description: 'Rasyonel sayılar ve işlemler',
        difficulty: 3,
        prerequisites: ['math_integers'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_algebraic_expressions',
        name: 'Cebirsel İfadeler',
        description: 'Değişken ve cebirsel ifadeler',
        difficulty: 3,
        prerequisites: ['math_rational_numbers'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_equations',
        name: 'Denklemler',
        description: 'Birinci dereceden denklemler',
        difficulty: 4,
        prerequisites: ['math_algebraic_expressions'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_inequalities',
        name: 'Eşitsizlikler',
        description: 'Birinci dereceden eşitsizlikler',
        difficulty: 4,
        prerequisites: ['math_equations'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_coordinate_system',
        name: 'Koordinat Sistemi',
        description: 'Koordinat düzlemi ve doğrusal fonksiyonlar',
        difficulty: 4,
        prerequisites: ['math_equations'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_statistics',
        name: 'İstatistik',
        description: 'Veri toplama, düzenleme ve yorumlama',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'math_probability',
        name: 'Olasılık',
        description: 'Basit olasılık hesaplamaları',
        difficulty: 3,
        prerequisites: ['math_statistics'],
        estimatedHours: 3,
      ),
      // Lise Matematik Konuları
      CurriculumTopic(
        id: 'math_functions',
        name: 'Fonksiyonlar',
        description: 'Fonksiyon kavramı ve özellikleri',
        difficulty: 4,
        prerequisites: ['math_coordinate_system'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'math_quadratic_functions',
        name: 'İkinci Dereceden Fonksiyonlar',
        description: 'Parabol ve ikinci dereceden denklemler',
        difficulty: 4,
        prerequisites: ['math_functions'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_trigonometry',
        name: 'Trigonometri',
        description: 'Trigonometrik oranlar ve fonksiyonlar',
        difficulty: 4,
        prerequisites: ['math_triangles'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'math_logarithm',
        name: 'Logaritma',
        description: 'Logaritma kavramı ve özellikleri',
        difficulty: 4,
        prerequisites: ['math_functions'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'math_sequences',
        name: 'Diziler',
        description: 'Aritmetik ve geometrik diziler',
        difficulty: 4,
        prerequisites: ['math_functions'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'math_limits',
        name: 'Limit',
        description: 'Limit kavramı ve hesaplamaları',
        difficulty: 5,
        prerequisites: ['math_functions'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'math_derivatives',
        name: 'Türev',
        description: 'Türev kavramı ve uygulamaları',
        difficulty: 5,
        prerequisites: ['math_limits'],
        estimatedHours: 7,
      ),
      CurriculumTopic(
        id: 'math_integrals',
        name: 'İntegral',
        description: 'İntegral kavramı ve uygulamaları',
        difficulty: 5,
        prerequisites: ['math_derivatives'],
        estimatedHours: 7,
      ),
    ];
  }

  static List<CurriculumTopic> _getMathTopicsForLevel(EducationLevel level) {
    final allTopics = _getMathTopics();

    switch (level) {
      case EducationLevel.grade5:
        return allTopics.where((topic) => [
          'math_natural_numbers',
          'math_fractions',
          'math_decimal_numbers',
          'math_basic_geometry',
          'math_statistics',
        ].contains(topic.id)).toList();

      case EducationLevel.grade6:
        return allTopics.where((topic) => [
          'math_natural_numbers',
          'math_fractions',
          'math_decimal_numbers',
          'math_percentage',
          'math_basic_geometry',
          'math_triangles',
          'math_area_perimeter',
          'math_statistics',
        ].contains(topic.id)).toList();

      case EducationLevel.grade7:
        return allTopics.where((topic) => [
          'math_integers',
          'math_rational_numbers',
          'math_algebraic_expressions',
          'math_triangles',
          'math_area_perimeter',
          'math_statistics',
          'math_probability',
        ].contains(topic.id)).toList();

      case EducationLevel.grade8:
        return allTopics.where((topic) => [
          'math_rational_numbers',
          'math_algebraic_expressions',
          'math_equations',
          'math_inequalities',
          'math_coordinate_system',
          'math_statistics',
          'math_probability',
        ].contains(topic.id)).toList();

      case EducationLevel.grade9:
        return allTopics.where((topic) => [
          'math_equations',
          'math_inequalities',
          'math_coordinate_system',
          'math_functions',
          'math_quadratic_functions',
          'math_statistics',
          'math_probability',
        ].contains(topic.id)).toList();

      case EducationLevel.grade10:
        return allTopics.where((topic) => [
          'math_functions',
          'math_quadratic_functions',
          'math_trigonometry',
          'math_logarithm',
          'math_sequences',
        ].contains(topic.id)).toList();

      case EducationLevel.grade11:
        return allTopics.where((topic) => [
          'math_trigonometry',
          'math_logarithm',
          'math_sequences',
          'math_limits',
          'math_derivatives',
        ].contains(topic.id)).toList();

      case EducationLevel.grade12:
        return allTopics.where((topic) => [
          'math_limits',
          'math_derivatives',
          'math_integrals',
        ].contains(topic.id)).toList();

      case EducationLevel.graduate:
        return allTopics; // Mezunlar için tüm konular
    }
  }

  static List<CurriculumTopic> _getTurkishTopics() {
    return [
      // Ortaokul Türkçe Konuları
      CurriculumTopic(
        id: 'turkish_reading_comprehension',
        name: 'Okuma',
        description: 'Metin türleri, okuma stratejileri ve anlama',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'turkish_listening',
        name: 'Dinleme/İzleme',
        description: 'Dinleme becerileri ve anlama',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'turkish_speaking',
        name: 'Konuşma',
        description: 'Sözlü anlatım ve sunum becerileri',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'turkish_writing',
        name: 'Yazma',
        description: 'Yazılı anlatım ve metin türleri',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'turkish_grammar',
        name: 'Dil Bilgisi',
        description: 'Ses bilgisi, kelime bilgisi, cümle bilgisi',
        difficulty: 3,
        estimatedHours: 5,
      ),
    ];
  }

  static List<CurriculumTopic> _getTurkishLiteratureTopics() {
    return [
      // Lise Türk Dili ve Edebiyatı Konuları
      CurriculumTopic(
        id: 'literature_reading',
        name: 'Okuma',
        description: 'Edebi metin analizi ve yorumlama',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'literature_writing',
        name: 'Yazma',
        description: 'Edebi türlerde yazma ve kompozisyon',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'literature_speaking',
        name: 'Konuşma',
        description: 'Edebi metinler üzerine tartışma ve sunum',
        difficulty: 3,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'literature_listening',
        name: 'Dinleme/İzleme',
        description: 'Edebi metinleri dinleme ve anlama',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'literature_language',
        name: 'Dil Bilgisi',
        description: 'Türkçenin ses, biçim, cümle ve anlam bilgisi',
        difficulty: 4,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'literature_history',
        name: 'Edebiyat Tarihi',
        description: 'Türk edebiyatının tarihsel gelişimi',
        difficulty: 4,
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'literature_genres',
        name: 'Edebi Türler',
        description: 'Şiir, hikaye, roman, tiyatro, deneme',
        difficulty: 4,
        prerequisites: ['literature_reading'],
        estimatedHours: 5,
      ),
    ];
  }

  static List<CurriculumTopic> _getScienceTopics() {
    return [
      // 5. Sınıf Fen Bilimleri
      CurriculumTopic(
        id: 'science_living_beings',
        name: 'Canlılar Dünyasını Gezelim, Tanıyalım',
        description: 'Canlıların temel özellikleri ve sınıflandırılması',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_human_body',
        name: 'Vücudumuzun Bilmecesini Çözelim',
        description: 'İnsan vücudu sistemleri',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_matter_heat',
        name: 'Kuvvetin Büyüklüğünü Ölçelim',
        description: 'Kuvvet, hareket ve enerji',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_matter_properties',
        name: 'Maddeyi Tanıyalım',
        description: 'Maddenin özellikleri ve halleri',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_light_sound',
        name: 'Işığın ve Sesin Yayılması',
        description: 'Işık ve ses olayları',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'science_earth_universe',
        name: 'Dünya, Güneş ve Ay',
        description: 'Gök cisimleri ve hareketleri',
        difficulty: 2,
        estimatedHours: 3,
      ),

      // 6. Sınıf Fen Bilimleri
      CurriculumTopic(
        id: 'science_diversity_unity',
        name: 'Yaşamın Temel Birimi: Hücre',
        description: 'Hücre yapısı ve işlevleri',
        difficulty: 3,
        prerequisites: ['science_living_beings'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_systems_body',
        name: 'Vücudumuzdaki Sistemler',
        description: 'Dolaşım, solunum, sindirim sistemleri',
        difficulty: 3,
        prerequisites: ['science_human_body'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'science_force_motion',
        name: 'Kuvvet ve Hareket',
        description: 'Kuvvetin etkileri ve hareket türleri',
        difficulty: 3,
        prerequisites: ['science_matter_heat'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_matter_heat_temp',
        name: 'Madde ve Isı',
        description: 'Isı ve sıcaklık, hal değişimleri',
        difficulty: 3,
        prerequisites: ['science_matter_properties'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_electricity',
        name: 'Elektrik Devrelerinin Elemanları',
        description: 'Basit elektrik devreleri',
        difficulty: 3,
        estimatedHours: 4,
      ),

      // 7. Sınıf Fen Bilimleri
      CurriculumTopic(
        id: 'science_solar_system',
        name: 'Güneş Sistemi ve Ötesi',
        description: 'Gezegen sistemi ve uzay araştırmaları',
        difficulty: 3,
        prerequisites: ['science_earth_universe'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_photosynthesis',
        name: 'Fotosentez',
        description: 'Bitkilerde besin üretimi',
        difficulty: 3,
        prerequisites: ['science_diversity_unity'],
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'science_human_environment',
        name: 'İnsan ve Çevre',
        description: 'Çevre sorunları ve korunması',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_pure_mixtures',
        name: 'Saf Madde ve Karışımlar',
        description: 'Madde türleri ve ayrıştırma yöntemleri',
        difficulty: 3,
        prerequisites: ['science_matter_heat_temp'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_acids_bases',
        name: 'Asitler ve Bazlar',
        description: 'Asit-baz kavramı ve günlük yaşamda kullanımı',
        difficulty: 3,
        prerequisites: ['science_pure_mixtures'],
        estimatedHours: 3,
      ),

      // 8. Sınıf Fen Bilimleri
      CurriculumTopic(
        id: 'science_dna_genetic',
        name: 'DNA ve Genetik Kod',
        description: 'Kalıtım ve genetik çeşitlilik',
        difficulty: 4,
        prerequisites: ['science_diversity_unity'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_reproduction',
        name: 'Üreme, Büyüme ve Gelişim',
        description: 'Canlılarda üreme ve gelişim',
        difficulty: 4,
        prerequisites: ['science_dna_genetic'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_pressure',
        name: 'Basınç',
        description: 'Katı, sıvı ve gazlarda basınç',
        difficulty: 4,
        prerequisites: ['science_force_motion'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'science_energy',
        name: 'Enerji Dönüşümleri ve Çevre Bilimi',
        description: 'Enerji türleri ve dönüşümleri',
        difficulty: 4,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'science_atoms',
        name: 'Maddenin Yapısı ve Özellikleri',
        description: 'Atom modeli ve periyodik tablo',
        difficulty: 4,
        prerequisites: ['science_acids_bases'],
        estimatedHours: 5,
      ),
    ];
  }

  static List<CurriculumTopic> _getScienceTopicsForLevel(EducationLevel level) {
    final allTopics = _getScienceTopics();

    switch (level) {
      case EducationLevel.grade5:
        return allTopics.where((topic) => [
          'science_living_beings',
          'science_human_body',
          'science_matter_heat',
          'science_matter_properties',
          'science_light_sound',
          'science_earth_universe',
        ].contains(topic.id)).toList();

      case EducationLevel.grade6:
        return allTopics.where((topic) => [
          'science_diversity_unity',
          'science_systems_body',
          'science_force_motion',
          'science_matter_heat_temp',
          'science_electricity',
        ].contains(topic.id)).toList();

      case EducationLevel.grade7:
        return allTopics.where((topic) => [
          'science_solar_system',
          'science_photosynthesis',
          'science_human_environment',
          'science_pure_mixtures',
          'science_acids_bases',
        ].contains(topic.id)).toList();

      case EducationLevel.grade8:
        return allTopics.where((topic) => [
          'science_dna_genetic',
          'science_reproduction',
          'science_pressure',
          'science_energy',
          'science_atoms',
        ].contains(topic.id)).toList();

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getSocialStudiesTopicsForLevel(EducationLevel level) {
    final allTopics = _getSocialStudiesTopics();

    switch (level) {
      case EducationLevel.grade5:
        return allTopics.where((topic) => [
          'social_individual_society',
          'social_culture_heritage',
          'social_people_places',
          'social_science_technology',
          'social_production_distribution',
          'social_active_citizenship',
          'social_global_connections',
        ].contains(topic.id)).toList();

      case EducationLevel.grade6:
        return allTopics.where((topic) => [
          'social_learning_area_6',
        ].contains(topic.id)).toList();

      case EducationLevel.grade7:
        return allTopics.where((topic) => [
          'social_learning_area_7',
        ].contains(topic.id)).toList();

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getEnglishTopicsForLevel(EducationLevel level) {
    final allTopics = _getEnglishTopics();

    // Ortaokul ve lise için farklı konular
    if ([EducationLevel.grade5, EducationLevel.grade6, EducationLevel.grade7, EducationLevel.grade8].contains(level)) {
      return allTopics.where((topic) => [
        'english_basic_vocabulary',
        'english_grammar_basics',
        'english_listening',
        'english_speaking',
        'english_reading',
        'english_writing',
      ].contains(topic.id)).toList();
    } else {
      return allTopics; // Lise için tüm konular
    }
  }

  static List<CurriculumTopic> _getPhysicsTopicsForLevel(EducationLevel level) {
    final allTopics = _getPhysicsTopics();

    switch (level) {
      case EducationLevel.grade9:
        return allTopics.where((topic) => [
          'physics_motion',
          'physics_forces',
          'physics_energy',
          'physics_heat_temperature',
          'physics_waves_sound',
        ].contains(topic.id)).toList();

      case EducationLevel.grade10:
        return allTopics.where((topic) => [
          'physics_electric_magnetic',
          'physics_optics',
        ].contains(topic.id)).toList();

      case EducationLevel.grade11:
        return allTopics.where((topic) => [
          'physics_circular_motion',
          'physics_simple_harmonic',
          'physics_impulse_momentum',
        ].contains(topic.id)).toList();

      case EducationLevel.grade12:
        return allTopics.where((topic) => [
          'physics_electric_current',
          'physics_electromagnetic_induction',
          'physics_alternating_current',
          'physics_atomic_nuclear',
          'physics_modern',
        ].contains(topic.id)).toList();

      case EducationLevel.graduate:
        return allTopics; // Mezunlar için tüm konular

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getChemistryTopicsForLevel(EducationLevel level) {
    final allTopics = _getChemistryTopics();

    switch (level) {
      case EducationLevel.grade9:
        return allTopics.where((topic) => [
          'chemistry_intro',
          'chemistry_atom_structure',
          'chemistry_chemical_bonds',
          'chemistry_matter_nature',
          'chemistry_nature_of_matter',
        ].contains(topic.id)).toList();

      case EducationLevel.grade10:
        return allTopics.where((topic) => [
          'chemistry_mixtures',
          'chemistry_acids_bases',
          'chemistry_chemical_reactions',
        ].contains(topic.id)).toList();

      case EducationLevel.grade11:
        return allTopics.where((topic) => [
          'chemistry_modern_atom',
          'chemistry_gases',
          'chemistry_liquids_solids',
          'chemistry_solutions',
          'chemistry_reaction_rates',
          'chemistry_chemical_equilibrium',
        ].contains(topic.id)).toList();

      case EducationLevel.grade12:
        return allTopics.where((topic) => [
          'chemistry_carbon_compounds',
          'chemistry_organic_reactions',
          'chemistry_energy_entropy',
          'chemistry_electrochemistry',
        ].contains(topic.id)).toList();

      case EducationLevel.graduate:
        return allTopics; // Mezunlar için tüm konular

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getBiologyTopicsForLevel(EducationLevel level) {
    final allTopics = _getBiologyTopics();

    switch (level) {
      case EducationLevel.grade9:
        return allTopics.where((topic) => [
          'biology_intro',
          'biology_cell_division',
          'biology_genetics_basic',
          'biology_ecosystem',
        ].contains(topic.id)).toList();

      case EducationLevel.grade10:
        return allTopics.where((topic) => [
          'biology_cell_structure',
          'biology_transport',
          'biology_plant_biology',
          'biology_animal_biology',
        ].contains(topic.id)).toList();

      case EducationLevel.grade11:
        return allTopics.where((topic) => [
          'biology_circulatory',
          'biology_respiratory',
          'biology_excretory',
          'biology_nervous',
          'biology_endocrine',
          'biology_reproduction',
        ].contains(topic.id)).toList();

      case EducationLevel.grade12:
        return allTopics.where((topic) => [
          'biology_molecular',
          'biology_biotechnology',
          'biology_evolution',
          'biology_ecology',
        ].contains(topic.id)).toList();

      case EducationLevel.graduate:
        return allTopics; // Mezunlar için tüm konular

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getHistoryTopicsForLevel(EducationLevel level) {
    final allTopics = _getHistoryTopics();

    switch (level) {
      case EducationLevel.grade9:
        return allTopics.where((topic) => [
          'history_prehistoric',
          'history_ancient_civilizations',
          'history_classical_age',
          'history_islam',
          'history_turks_islam',
          'history_first_turkish_states',
        ].contains(topic.id)).toList();

      case EducationLevel.grade10:
        return allTopics.where((topic) => [
          'history_ottoman_foundation',
          'history_ottoman_classical',
          'history_ottoman_stagnation',
          'history_ottoman_decline',
        ].contains(topic.id)).toList();

      case EducationLevel.grade11:
        return allTopics.where((topic) => [
          'history_19th_century_reforms',
          'history_20th_century_ottoman',
          'history_world_war_1',
          'history_national_struggle',
        ].contains(topic.id)).toList();

      case EducationLevel.graduate:
        return allTopics; // Mezunlar için tüm konular

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getGeographyTopicsForLevel(EducationLevel level) {
    final allTopics = _getGeographyTopics();

    switch (level) {
      case EducationLevel.grade9:
        return allTopics.where((topic) => [
          'geography_intro',
          'geography_earth_shape',
          'geography_map_direction',
          'geography_atmosphere',
          'geography_hydrosphere',
          'geography_lithosphere',
          'geography_biosphere',
        ].contains(topic.id)).toList();

      case EducationLevel.grade10:
        return allTopics.where((topic) => [
          'geography_natural_systems',
          'geography_human_systems',
          'geography_global_environment',
          'geography_environment_society',
        ].contains(topic.id)).toList();

      case EducationLevel.grade11:
        return allTopics.where((topic) => [
          'geography_turkey_location',
          'geography_turkey_physical',
          'geography_turkey_human',
          'geography_turkey_regions',
        ].contains(topic.id)).toList();

      case EducationLevel.grade12:
        return allTopics.where((topic) => [
          'geography_turkey_environment',
          'geography_turkey_natural_disasters',
          'geography_spatial_analysis',
          'geography_global_issues',
        ].contains(topic.id)).toList();

      case EducationLevel.graduate:
        return allTopics; // Mezunlar için tüm konular

      default:
        return [];
    }
  }

  static List<CurriculumTopic> _getSocialStudiesTopics() {
    return [
      // 5. Sınıf Sosyal Bilgiler
      CurriculumTopic(
        id: 'social_individual_society',
        name: 'Birey ve Toplum',
        description: 'Birey, aile, okul ve toplum ilişkileri',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'social_culture_heritage',
        name: 'Kültür ve Miras',
        description: 'Kültürel değerler ve miras',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'social_people_places',
        name: 'İnsanlar, Yerler ve Çevreler',
        description: 'Coğrafi konum ve çevre ilişkisi',
        difficulty: 2,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'social_science_technology',
        name: 'Bilim, Teknoloji ve Toplum',
        description: 'Bilim ve teknolojinin toplumsal etkileri',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'social_production_distribution',
        name: 'Üretim, Dağıtım ve Tüketim',
        description: 'Ekonomik faaliyetler ve kaynak kullanımı',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'social_active_citizenship',
        name: 'Etkin Vatandaşlık',
        description: 'Vatandaşlık hakları ve sorumlulukları',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'social_global_connections',
        name: 'Küresel Bağlantılar',
        description: 'Ülkeler arası ilişkiler ve küreselleşme',
        difficulty: 3,
        estimatedHours: 3,
      ),

      // 6. Sınıf Sosyal Bilgiler
      CurriculumTopic(
        id: 'social_learning_area_6',
        name: 'Sosyal Bilgiler Öğrenme Alanları (6. Sınıf)',
        description: '6. sınıf sosyal bilgiler konuları',
        difficulty: 3,
        prerequisites: ['social_individual_society'],
        estimatedHours: 4,
      ),

      // 7. Sınıf Sosyal Bilgiler
      CurriculumTopic(
        id: 'social_learning_area_7',
        name: 'Sosyal Bilgiler Öğrenme Alanları (7. Sınıf)',
        description: '7. sınıf sosyal bilgiler konuları',
        difficulty: 3,
        prerequisites: ['social_learning_area_6'],
        estimatedHours: 4,
      ),
    ];
  }

  static List<CurriculumTopic> _getRevolutionHistoryTopics() {
    return [
      CurriculumTopic(
        id: 'revolution_ottoman_decline',
        name: 'Osmanlı Devleti\'nin Dağılma Dönemi',
        description: 'Osmanlı\'nın son dönemi ve sorunları',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'revolution_national_struggle',
        name: 'Millî Mücadele',
        description: 'Kurtuluş Savaşı ve Atatürk',
        difficulty: 3,
        prerequisites: ['revolution_ottoman_decline'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'revolution_republic_foundation',
        name: 'Türkiye Cumhuriyeti\'nin Kuruluşu',
        description: 'Cumhuriyetin ilanı ve ilk yıllar',
        difficulty: 3,
        prerequisites: ['revolution_national_struggle'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'revolution_ataturk_reforms',
        name: 'Atatürk İlkeleri ve İnkılâplar',
        description: 'Cumhuriyet dönemi reformları',
        difficulty: 4,
        prerequisites: ['revolution_republic_foundation'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'revolution_modern_turkey',
        name: 'Çağdaş Türkiye',
        description: 'Atatürk sonrası dönem ve çok partili hayat',
        difficulty: 4,
        prerequisites: ['revolution_ataturk_reforms'],
        estimatedHours: 4,
      ),
    ];
  }

  static List<CurriculumTopic> _getEnglishTopics() {
    return [
      // Temel İngilizce Konuları
      CurriculumTopic(
        id: 'english_basic_vocabulary',
        name: 'Temel Kelime Bilgisi',
        description: 'Günlük yaşamda kullanılan temel kelimeler',
        difficulty: 1,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'english_grammar_basics',
        name: 'Temel Gramer',
        description: 'Temel gramer yapıları ve kuralları',
        difficulty: 2,
        prerequisites: ['english_basic_vocabulary'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'english_listening',
        name: 'Dinleme Becerileri',
        description: 'İngilizce dinleme ve anlama',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'english_speaking',
        name: 'Konuşma Becerileri',
        description: 'İngilizce konuşma ve telaffuz',
        difficulty: 3,
        prerequisites: ['english_grammar_basics'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'english_reading',
        name: 'Okuma Becerileri',
        description: 'İngilizce metin okuma ve anlama',
        difficulty: 3,
        prerequisites: ['english_basic_vocabulary'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'english_writing',
        name: 'Yazma Becerileri',
        description: 'İngilizce yazılı anlatım',
        difficulty: 3,
        prerequisites: ['english_grammar_basics'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'english_advanced_grammar',
        name: 'İleri Gramer',
        description: 'Karmaşık gramer yapıları',
        difficulty: 4,
        prerequisites: ['english_grammar_basics'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'english_literature',
        name: 'İngiliz Edebiyatı',
        description: 'İngiliz edebiyatından seçmeler',
        difficulty: 4,
        prerequisites: ['english_reading'],
        estimatedHours: 5,
      ),
    ];
  }

  static List<CurriculumTopic> _getPhilosophyTopics() {
    return [
      CurriculumTopic(
        id: 'philosophy_intro',
        name: 'Felsefeye Giriş',
        description: 'Felsefe nedir? Temel kavramlar',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'philosophy_knowledge',
        name: 'Bilgi Felsefesi',
        description: 'Bilgi, doğruluk ve kesinlik',
        difficulty: 4,
        prerequisites: ['philosophy_intro'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'philosophy_science',
        name: 'Bilim Felsefesi',
        description: 'Bilimsel yöntem ve bilimsel devrimler',
        difficulty: 4,
        prerequisites: ['philosophy_knowledge'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'philosophy_ethics',
        name: 'Ahlak Felsefesi',
        description: 'Etik teoriler ve ahlaki değerler',
        difficulty: 4,
        prerequisites: ['philosophy_intro'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'philosophy_politics',
        name: 'Siyaset Felsefesi',
        description: 'Devlet, adalet ve özgürlük',
        difficulty: 4,
        prerequisites: ['philosophy_ethics'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'philosophy_art',
        name: 'Sanat Felsefesi',
        description: 'Güzellik, sanat ve estetik',
        difficulty: 4,
        prerequisites: ['philosophy_intro'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'philosophy_religion',
        name: 'Din Felsefesi',
        description: 'Tanrı, din ve inanç',
        difficulty: 4,
        prerequisites: ['philosophy_intro'],
        estimatedHours: 4,
      ),
    ];
  }

  static List<CurriculumTopic> _getPhysicsTopics() {
    return [
      // 9. Sınıf Fizik
      CurriculumTopic(
        id: 'physics_motion',
        name: 'Fizik Bilimine Giriş ve Hareket',
        description: 'Fizik bilimi, ölçme ve hareket kavramları',
        difficulty: 3,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'physics_forces',
        name: 'Kuvvet ve Hareket',
        description: 'Newton yasaları ve kuvvet türleri',
        difficulty: 3,
        prerequisites: ['physics_motion'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'physics_energy',
        name: 'Enerji',
        description: 'İş, güç, enerji ve momentum',
        difficulty: 4,
        prerequisites: ['physics_forces'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'physics_heat_temperature',
        name: 'Isı ve Sıcaklık',
        description: 'Isı, sıcaklık ve hal değişimleri',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'physics_waves_sound',
        name: 'Dalgalar',
        description: 'Dalga özellikleri ve ses dalgaları',
        difficulty: 4,
        estimatedHours: 5,
      ),

      // 10. Sınıf Fizik
      CurriculumTopic(
        id: 'physics_electric_magnetic',
        name: 'Elektrik ve Manyetizma',
        description: 'Elektrik alan, akım ve manyetik alan',
        difficulty: 4,
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'physics_optics',
        name: 'Optik',
        description: 'Işığın yayılması, yansıma ve kırılma',
        difficulty: 4,
        prerequisites: ['physics_waves_sound'],
        estimatedHours: 5,
      ),

      // 11. Sınıf Fizik
      CurriculumTopic(
        id: 'physics_circular_motion',
        name: 'Çembersel Hareket',
        description: 'Çembersel hareket ve kütle çekim',
        difficulty: 4,
        prerequisites: ['physics_forces'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'physics_simple_harmonic',
        name: 'Basit Harmonik Hareket',
        description: 'Salınım hareketi ve dalgalar',
        difficulty: 4,
        prerequisites: ['physics_waves_sound'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'physics_impulse_momentum',
        name: 'İmpuls ve Momentum',
        description: 'İmpuls, momentum ve çarpışmalar',
        difficulty: 4,
        prerequisites: ['physics_energy'],
        estimatedHours: 4,
      ),

      // 12. Sınıf Fizik
      CurriculumTopic(
        id: 'physics_electric_current',
        name: 'Elektrik Akımı',
        description: 'Ohm yasası ve elektrik devreleri',
        difficulty: 4,
        prerequisites: ['physics_electric_magnetic'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'physics_electromagnetic_induction',
        name: 'Manyetizma ve Elektromanyetik İndüksiyon',
        description: 'Faraday yasası ve elektromanyetik indüksiyon',
        difficulty: 5,
        prerequisites: ['physics_electric_current'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'physics_alternating_current',
        name: 'Alternatif Akım',
        description: 'AC devreler ve transformatörler',
        difficulty: 5,
        prerequisites: ['physics_electromagnetic_induction'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'physics_atomic_nuclear',
        name: 'Atom Fiziği ve Radyoaktivite',
        description: 'Atom modeli, radyoaktivite ve nükleer fizik',
        difficulty: 5,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'physics_modern',
        name: 'Modern Fizik',
        description: 'Kuantum fiziği ve görelilik teorisi',
        difficulty: 5,
        prerequisites: ['physics_atomic_nuclear'],
        estimatedHours: 4,
      ),
    ];
  }

  static List<CurriculumTopic> _getChemistryTopics() {
    return [
      // 9. Sınıf Kimya
      CurriculumTopic(
        id: 'chemistry_intro',
        name: 'Kimya Bilimi',
        description: 'Kimya biliminin tanımı ve önemi',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'chemistry_atom_structure',
        name: 'Atom ve Periyodik Sistem',
        description: 'Atom yapısı, elektron dizilimi ve periyodik tablo',
        difficulty: 3,
        prerequisites: ['chemistry_intro'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_chemical_bonds',
        name: 'Kimyasal Türler Arası Etkileşimler',
        description: 'İyonik, kovalent ve metalik bağlar',
        difficulty: 4,
        prerequisites: ['chemistry_atom_structure'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_matter_nature',
        name: 'Maddenin Halleri',
        description: 'Katı, sıvı, gaz halleri ve geçişler',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'chemistry_nature_of_matter',
        name: 'Doğa ve Kimya',
        description: 'Doğadaki kimyasal olaylar',
        difficulty: 3,
        estimatedHours: 3,
      ),

      // 10. Sınıf Kimya
      CurriculumTopic(
        id: 'chemistry_mixtures',
        name: 'Karışımlar',
        description: 'Homojen ve heterojen karışımlar, çözeltiler',
        difficulty: 3,
        prerequisites: ['chemistry_matter_nature'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'chemistry_acids_bases',
        name: 'Asitler, Bazlar ve Tuzlar',
        description: 'Asit-baz teorileri ve pH kavramı',
        difficulty: 4,
        prerequisites: ['chemistry_mixtures'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_chemical_reactions',
        name: 'Kimyasal Tepkimeler',
        description: 'Tepkime türleri ve denklem yazımı',
        difficulty: 4,
        prerequisites: ['chemistry_chemical_bonds'],
        estimatedHours: 5,
      ),

      // 11. Sınıf Kimya
      CurriculumTopic(
        id: 'chemistry_modern_atom',
        name: 'Modern Atom Teorisi',
        description: 'Kuantum mekaniği ve atom orbitalleri',
        difficulty: 5,
        prerequisites: ['chemistry_atom_structure'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_gases',
        name: 'Gazlar',
        description: 'Gaz yasaları ve kinetik teori',
        difficulty: 4,
        prerequisites: ['chemistry_matter_nature'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'chemistry_liquids_solids',
        name: 'Sıvılar ve Katılar',
        description: 'Sıvı ve katı hal özellikleri',
        difficulty: 4,
        prerequisites: ['chemistry_gases'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'chemistry_solutions',
        name: 'Çözeltiler ve Çözünürlük',
        description: 'Çözelti konsantrasyonu ve çözünürlük',
        difficulty: 4,
        prerequisites: ['chemistry_mixtures'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_reaction_rates',
        name: 'Tepkimelerde Hız',
        description: 'Tepkime hızı ve etkileyen faktörler',
        difficulty: 4,
        prerequisites: ['chemistry_chemical_reactions'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'chemistry_chemical_equilibrium',
        name: 'Kimyasal Denge',
        description: 'Denge sabiti ve Le Chatelier ilkesi',
        difficulty: 5,
        prerequisites: ['chemistry_reaction_rates'],
        estimatedHours: 5,
      ),

      // 12. Sınıf Kimya
      CurriculumTopic(
        id: 'chemistry_carbon_compounds',
        name: 'Karbon Kimyasına Giriş',
        description: 'Organik bileşikler ve fonksiyonel gruplar',
        difficulty: 4,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_organic_reactions',
        name: 'Organik Bileşikler',
        description: 'Organik tepkimeler ve mekanizmalar',
        difficulty: 5,
        prerequisites: ['chemistry_carbon_compounds'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'chemistry_energy_entropy',
        name: 'Enerji ve Entropi',
        description: 'Termodinamik ve enerji değişimleri',
        difficulty: 5,
        prerequisites: ['chemistry_chemical_equilibrium'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'chemistry_electrochemistry',
        name: 'Elektrokimya',
        description: 'Redoks tepkimeleri ve elektroliz',
        difficulty: 5,
        prerequisites: ['chemistry_energy_entropy'],
        estimatedHours: 5,
      ),
    ];
  }

  static List<CurriculumTopic> _getBiologyTopics() {
    return [
      // 9. Sınıf Biyoloji
      CurriculumTopic(
        id: 'biology_intro',
        name: 'Biyoloji Bilimi',
        description: 'Biyoloji biliminin tanımı ve çalışma alanları',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'biology_cell_division',
        name: 'Hücre Bölünmeleri',
        description: 'Mitoz ve mayoz bölünme',
        difficulty: 3,
        prerequisites: ['biology_intro'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'biology_genetics_basic',
        name: 'Kalıtımın Genel İlkeleri',
        description: 'Mendel yasaları ve kalıtım',
        difficulty: 3,
        prerequisites: ['biology_cell_division'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'biology_ecosystem',
        name: 'Canlılar ve Çevre',
        description: 'Ekosistem, biyom ve çevre sorunları',
        difficulty: 3,
        estimatedHours: 4,
      ),

      // 10. Sınıf Biyoloji
      CurriculumTopic(
        id: 'biology_cell_structure',
        name: 'Hücre: Yaşamın Temel Birimi',
        description: 'Hücre yapısı, organeller ve işlevleri',
        difficulty: 3,
        prerequisites: ['biology_intro'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'biology_transport',
        name: 'Canlılarda Enerji Dönüşümleri',
        description: 'Fotosentez, solunum ve ATP',
        difficulty: 4,
        prerequisites: ['biology_cell_structure'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'biology_plant_biology',
        name: 'Bitki Biyolojisi',
        description: 'Bitki anatomisi ve fizyolojisi',
        difficulty: 3,
        prerequisites: ['biology_transport'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'biology_animal_biology',
        name: 'Hayvan Biyolojisi',
        description: 'Hayvan sistemleri ve fizyolojisi',
        difficulty: 4,
        prerequisites: ['biology_plant_biology'],
        estimatedHours: 5,
      ),

      // 11. Sınıf Biyoloji
      CurriculumTopic(
        id: 'biology_circulatory',
        name: 'Dolaşım Sistemi',
        description: 'Kan dolaşımı ve kalp',
        difficulty: 4,
        prerequisites: ['biology_animal_biology'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'biology_respiratory',
        name: 'Solunum Sistemi',
        description: 'Solunum organları ve gaz değişimi',
        difficulty: 4,
        prerequisites: ['biology_circulatory'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'biology_excretory',
        name: 'Boşaltım Sistemi',
        description: 'Böbrek ve boşaltım',
        difficulty: 4,
        prerequisites: ['biology_respiratory'],
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'biology_nervous',
        name: 'Sinir Sistemi',
        description: 'Sinir sistemi ve davranış',
        difficulty: 4,
        prerequisites: ['biology_excretory'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'biology_endocrine',
        name: 'Duyu Organları ve Endokrin Sistem',
        description: 'Hormonlar ve duyu organları',
        difficulty: 4,
        prerequisites: ['biology_nervous'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'biology_reproduction',
        name: 'Üreme Sistemi ve Embriyonik Gelişim',
        description: 'Üreme ve gelişim',
        difficulty: 4,
        prerequisites: ['biology_endocrine'],
        estimatedHours: 4,
      ),

      // 12. Sınıf Biyoloji
      CurriculumTopic(
        id: 'biology_molecular',
        name: 'Moleküler Genetik',
        description: 'DNA, RNA ve protein sentezi',
        difficulty: 5,
        prerequisites: ['biology_genetics_basic'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'biology_biotechnology',
        name: 'Biyoteknoloji',
        description: 'Genetik mühendisliği ve uygulamaları',
        difficulty: 5,
        prerequisites: ['biology_molecular'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'biology_evolution',
        name: 'Canlıların Çeşitliliği ve Sınıflandırılması',
        description: 'Evrim teorisi ve taksonomı',
        difficulty: 4,
        prerequisites: ['biology_molecular'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'biology_ecology',
        name: 'Ekoloji ve Güncel Çevre Sorunları',
        description: 'Ekolojik ilişkiler ve çevre koruma',
        difficulty: 4,
        prerequisites: ['biology_ecosystem'],
        estimatedHours: 4,
      ),
    ];
  }

  static List<CurriculumTopic> _getHistoryTopics() {
    return [
      // 9. Sınıf Tarih
      CurriculumTopic(
        id: 'history_prehistoric',
        name: 'Tarih Öncesi Çağlar',
        description: 'İnsanlığın ilk dönemleri ve Anadolu',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'history_ancient_civilizations',
        name: 'İlk Uygarlıklar',
        description: 'Mezopotamya, Mısır ve Anadolu uygarlıkları',
        difficulty: 3,
        prerequisites: ['history_prehistoric'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'history_classical_age',
        name: 'Antik Çağ',
        description: 'Yunan ve Roma uygarlıkları',
        difficulty: 3,
        prerequisites: ['history_ancient_civilizations'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'history_islam',
        name: 'İslam Medeniyetinin Doğuşu',
        description: 'İslam\'ın doğuşu ve yayılışı',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'history_turks_islam',
        name: 'Türklerin İslamiyet\'i Kabulü',
        description: 'Türklerin İslamiyet\'i kabul süreci',
        difficulty: 3,
        prerequisites: ['history_islam'],
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'history_first_turkish_states',
        name: 'İlk Türk-İslam Devletleri',
        description: 'Gazneliler, Selçuklular ve Anadolu\'nun Türkleşmesi',
        difficulty: 3,
        prerequisites: ['history_turks_islam'],
        estimatedHours: 5,
      ),

      // 10. Sınıf Tarih
      CurriculumTopic(
        id: 'history_ottoman_foundation',
        name: 'Osmanlı Devleti\'nin Kuruluşu',
        description: 'Osmanlı\'nın kuruluş dönemi',
        difficulty: 3,
        prerequisites: ['history_first_turkish_states'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'history_ottoman_classical',
        name: 'Osmanlı Devleti\'nin Yükselme Dönemi',
        description: 'Klasik dönem Osmanlı',
        difficulty: 3,
        prerequisites: ['history_ottoman_foundation'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'history_ottoman_stagnation',
        name: 'Osmanlı Devleti\'nin Duraklama Dönemi',
        description: 'Duraklama dönemi ve sorunları',
        difficulty: 3,
        prerequisites: ['history_ottoman_classical'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'history_ottoman_decline',
        name: 'Osmanlı Devleti\'nin Dağılma Dönemi',
        description: 'Gerileme dönemi ve reform çabaları',
        difficulty: 4,
        prerequisites: ['history_ottoman_stagnation'],
        estimatedHours: 5,
      ),

      // 11. Sınıf Tarih
      CurriculumTopic(
        id: 'history_19th_century_reforms',
        name: '19. Yüzyılda Osmanlı Devleti',
        description: 'Tanzimat ve Islahat fermanları',
        difficulty: 4,
        prerequisites: ['history_ottoman_decline'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'history_20th_century_ottoman',
        name: '20. Yüzyıl Başlarında Osmanlı Devleti',
        description: 'II. Meşrutiyet ve Balkan Savaşları',
        difficulty: 4,
        prerequisites: ['history_19th_century_reforms'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'history_world_war_1',
        name: 'Birinci Dünya Savaşı',
        description: 'Osmanlı\'nın I. Dünya Savaşı\'na katılımı',
        difficulty: 4,
        prerequisites: ['history_20th_century_ottoman'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'history_national_struggle',
        name: 'Millî Mücadele',
        description: 'Kurtuluş Savaşı ve Atatürk',
        difficulty: 4,
        prerequisites: ['history_world_war_1'],
        estimatedHours: 6,
      ),
    ];
  }

  static List<CurriculumTopic> _getGeographyTopics() {
    return [
      // 9. Sınıf Coğrafya
      CurriculumTopic(
        id: 'geography_intro',
        name: 'Coğrafya Bilimi',
        description: 'Coğrafya biliminin tanımı ve çalışma alanları',
        difficulty: 2,
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'geography_earth_shape',
        name: 'Dünya\'nın Şekli ve Özellikleri',
        description: 'Dünya\'nın şekli, boyutları ve hareketleri',
        difficulty: 3,
        prerequisites: ['geography_intro'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_map_direction',
        name: 'Harita Bilgisi ve Yön Tayini',
        description: 'Harita okuma ve koordinat sistemi',
        difficulty: 3,
        prerequisites: ['geography_earth_shape'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_atmosphere',
        name: 'Atmosfer ve İklim',
        description: 'Atmosfer katmanları ve iklim elemanları',
        difficulty: 3,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'geography_hydrosphere',
        name: 'Hidrosfer',
        description: 'Su döngüsü ve su kaynakları',
        difficulty: 3,
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_lithosphere',
        name: 'Litosfer',
        description: 'Yer şekilleri ve jeolojik süreçler',
        difficulty: 3,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'geography_biosphere',
        name: 'Biyosfer',
        description: 'Bitki örtüsü ve toprak',
        difficulty: 3,
        prerequisites: ['geography_atmosphere', 'geography_hydrosphere'],
        estimatedHours: 4,
      ),

      // 10. Sınıf Coğrafya
      CurriculumTopic(
        id: 'geography_natural_systems',
        name: 'Doğal Sistemler',
        description: 'Ekosistemlerin işleyişi',
        difficulty: 3,
        prerequisites: ['geography_biosphere'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_human_systems',
        name: 'Beşeri Sistemler',
        description: 'Nüfus, yerleşme ve ekonomik faaliyetler',
        difficulty: 4,
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'geography_global_environment',
        name: 'Küresel Ortam: Bölgeler ve Ülkeler',
        description: 'Dünya\'nın bölgesel coğrafyası',
        difficulty: 4,
        prerequisites: ['geography_human_systems'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'geography_environment_society',
        name: 'Çevre ve Toplum',
        description: 'Çevre sorunları ve sürdürülebilirlik',
        difficulty: 4,
        prerequisites: ['geography_natural_systems'],
        estimatedHours: 4,
      ),

      // 11. Sınıf Coğrafya
      CurriculumTopic(
        id: 'geography_turkey_location',
        name: 'Türkiye\'nin Coğrafi Konumu',
        description: 'Türkiye\'nin konum özellikleri',
        difficulty: 3,
        prerequisites: ['geography_map_direction'],
        estimatedHours: 3,
      ),
      CurriculumTopic(
        id: 'geography_turkey_physical',
        name: 'Türkiye\'nin Fiziki Coğrafyası',
        description: 'Türkiye\'nin yer şekilleri, iklimi ve hidrografyası',
        difficulty: 4,
        prerequisites: ['geography_turkey_location'],
        estimatedHours: 6,
      ),
      CurriculumTopic(
        id: 'geography_turkey_human',
        name: 'Türkiye\'nin Beşeri ve Ekonomik Coğrafyası',
        description: 'Türkiye\'nin nüfus ve ekonomik özellikleri',
        difficulty: 4,
        prerequisites: ['geography_turkey_physical'],
        estimatedHours: 5,
      ),
      CurriculumTopic(
        id: 'geography_turkey_regions',
        name: 'Türkiye\'nin Bölgeleri',
        description: 'Coğrafi bölgeler ve özellikleri',
        difficulty: 4,
        prerequisites: ['geography_turkey_human'],
        estimatedHours: 6,
      ),

      // 12. Sınıf Coğrafya
      CurriculumTopic(
        id: 'geography_turkey_environment',
        name: 'Türkiye\'de Çevre ve Çevre Sorunları',
        description: 'Türkiye\'deki çevre sorunları ve çözümleri',
        difficulty: 4,
        prerequisites: ['geography_turkey_regions'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_turkey_natural_disasters',
        name: 'Türkiye\'de Doğal Afetler',
        description: 'Deprem, sel, heyelan gibi doğal afetler',
        difficulty: 4,
        prerequisites: ['geography_turkey_physical'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_spatial_analysis',
        name: 'Mekânsal Analiz',
        description: 'CBS ve uzaktan algılama',
        difficulty: 5,
        prerequisites: ['geography_map_direction'],
        estimatedHours: 4,
      ),
      CurriculumTopic(
        id: 'geography_global_issues',
        name: 'Küresel Sorunlar ve Sürdürülebilirlik',
        description: 'Küresel ısınma, çevre koruma ve sürdürülebilir kalkınma',
        difficulty: 4,
        prerequisites: ['geography_environment_society'],
        estimatedHours: 4,
      ),
    ];
  }
}
