import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/time_entry.dart';
import '../services/storage_service.dart';
import 'package:uuid/uuid.dart';

class TimeTrackingProvider with ChangeNotifier {
  final StorageService _storageService = StorageService();
  final Uuid _uuid = const Uuid();
  
  List<TimeEntry> _timeEntries = [];
  bool _isLoading = false;
  
  // Pomodoro Timer
  Timer? _timer;
  int _remainingSeconds = 25 * 60; // 25 minutes default
  bool _isRunning = false;
  bool _isPaused = false;
  int _pomodoroCount = 0;
  String? _currentSubject;

  List<TimeEntry> get timeEntries => _timeEntries;
  bool get isLoading => _isLoading;
  int get remainingSeconds => _remainingSeconds;
  bool get isRunning => _isRunning;
  bool get isPaused => _isPaused;
  int get pomodoroCount => _pomodoroCount;
  String? get currentSubject => _currentSubject;

  String get formattedTime {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  TimeTrackingProvider() {
    loadTimeEntries();
  }

  Future<void> loadTimeEntries() async {
    _isLoading = true;
    notifyListeners();

    try {
      _timeEntries = await _storageService.getTimeEntries();
    } catch (e) {
      debugPrint('Error loading time entries: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> startTimer({String? subject, int minutes = 25}) async {
    if (_isRunning) return;

    _currentSubject = subject;
    _remainingSeconds = minutes * 60;
    _isRunning = true;
    _isPaused = false;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        _remainingSeconds--;
        notifyListeners();
      } else {
        _completePomodoro();
      }
    });

    notifyListeners();
  }

  void pauseTimer() {
    if (_timer != null && _isRunning) {
      _timer!.cancel();
      _isPaused = true;
      _isRunning = false;
      notifyListeners();
    }
  }

  void resumeTimer() {
    if (_isPaused) {
      _isRunning = true;
      _isPaused = false;
      
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
          notifyListeners();
        } else {
          _completePomodoro();
        }
      });
      
      notifyListeners();
    }
  }

  void stopTimer() {
    _timer?.cancel();
    _isRunning = false;
    _isPaused = false;
    _remainingSeconds = 25 * 60;
    _currentSubject = null;
    notifyListeners();
  }

  void _completePomodoro() {
    _timer?.cancel();
    _isRunning = false;
    _isPaused = false;
    _pomodoroCount++;

    if (_currentSubject != null) {
      _addPomodoroEntry(_currentSubject!);
    }

    _remainingSeconds = 25 * 60;
    _currentSubject = null;
    notifyListeners();
  }

  Future<void> _addPomodoroEntry(String subject) async {
    final entry = TimeEntry(
      id: _uuid.v4(),
      subject: subject,
      startTime: DateTime.now().subtract(const Duration(minutes: 25)),
      endTime: DateTime.now(),
      durationMinutes: 25,
      isPomodoro: true,
      pomodoroCount: 1,
    );

    await addTimeEntry(entry);
  }

  Future<void> addTimeEntry(TimeEntry entry) async {
    try {
      _timeEntries.add(entry);
      await _storageService.saveTimeEntries(_timeEntries);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding time entry: $e');
    }
  }

  Future<void> updateTimeEntry(TimeEntry updatedEntry) async {
    try {
      final index = _timeEntries.indexWhere((entry) => entry.id == updatedEntry.id);
      if (index != -1) {
        _timeEntries[index] = updatedEntry;
        await _storageService.saveTimeEntries(_timeEntries);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating time entry: $e');
    }
  }

  Future<void> deleteTimeEntry(String entryId) async {
    try {
      _timeEntries.removeWhere((entry) => entry.id == entryId);
      await _storageService.saveTimeEntries(_timeEntries);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting time entry: $e');
    }
  }

  List<TimeEntry> getTodayEntries() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return _timeEntries.where((entry) =>
      entry.startTime.isAfter(todayStart) &&
      entry.startTime.isBefore(todayEnd)
    ).toList();
  }

  int getTodayTotalMinutes() {
    final todayEntries = getTodayEntries();
    return todayEntries.fold<int>(0, (sum, entry) => 
      sum + (entry.durationMinutes ?? 0));
  }

  Map<String, int> getSubjectTimeDistribution() {
    final Map<String, int> distribution = {};
    
    for (final entry in _timeEntries) {
      final subject = entry.subject;
      final minutes = entry.durationMinutes ?? 0;
      distribution[subject] = (distribution[subject] ?? 0) + minutes;
    }
    
    return distribution;
  }

  List<TimeEntry> getWeekEntries() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 7));

    return _timeEntries.where((entry) =>
      entry.startTime.isAfter(weekStart) &&
      entry.startTime.isBefore(weekEnd)
    ).toList();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
