import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dashboard_screen.dart';
import 'study_plan_screen.dart';
import 'goals_screen.dart';
import 'time_tracking_screen.dart';
import 'chat_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const StudyPlanScreen(),
    const GoalsScreen(),
    const TimeTrackingScreen(),
    const ChatScreen(),
  ];

  final List<BottomNavigationBarItem> _navItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard),
      label: 'Ana Sayfa',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.calendar_today),
      label: '<PERSON><PERSON><PERSON>ş<PERSON>ı',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.flag),
      label: 'Hedefle<PERSON>',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.clock),
      label: 'Zaman',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.robot),
      label: 'AI Koç',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Colors.grey,
        items: _navItems,
      ),
    );
  }
}
