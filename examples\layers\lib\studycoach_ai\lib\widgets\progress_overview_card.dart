import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

class ProgressOverviewCard extends StatelessWidget {
  final double progress;

  const ProgressOverviewCard({
    super.key,
    required this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.trending_up,
                  color: Colors.green,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Genel İlerleme',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Center(
              child: CircularPercentIndicator(
                radius: 60.0,
                lineWidth: 8.0,
                percent: progress,
                center: Text(
                  '${(progress * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                progressColor: _getProgressColor(progress),
                backgroundColor: Colors.grey[300]!,
                circularStrokeCap: CircularStrokeCap.round,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _getProgressMessage(progress),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return Colors.red;
    } else if (progress < 0.7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _getProgressMessage(double progress) {
    if (progress < 0.3) {
      return 'Hedeflerine ulaşmak için daha fazla çalışman gerekiyor. Küçük adımlarla başla!';
    } else if (progress < 0.7) {
      return 'İyi gidiyorsun! Hedeflerine yaklaşıyorsun. Devam et!';
    } else {
      return 'Harika! Hedeflerinde çok iyi ilerleme kaydediyorsun. Böyle devam!';
    }
  }
}
