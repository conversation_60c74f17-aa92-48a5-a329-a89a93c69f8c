import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../../providers/user_provider.dart';
import '../../models/user.dart';
import '../../models/schedule_entry.dart';
import '../home_screen.dart';

class RegistrationScreen extends StatefulWidget {
  final UserType userType;

  const RegistrationScreen({super.key, required this.userType});

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final PageController _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  
  int _currentPage = 0;
  bool _isLoading = false;

  // Form verileri
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();

  DateTime? _selectedBirthDate;
  
  EducationLevel _selectedEducationLevel = EducationLevel.grade7;
  StudyStyle _selectedStudyStyle = StudyStyle.visual;
  int _dailyStudyGoal = 90; // dakika
  List<String> _selectedSubjects = [];
  List<String> _selectedStudyDays = [];
  List<ScheduleEntry> _scheduleEntries = [];

  List<String> get _availableSubjects {
    // Ortaokul (5-8. sınıf) - LGS kapsamındaki dersler
    if ([EducationLevel.grade5, EducationLevel.grade6, EducationLevel.grade7, EducationLevel.grade8].contains(_selectedEducationLevel)) {
      return [
        'Matematik',
        'Türkçe',
        'Fen Bilimleri',
        'Sosyal Bilgiler',
        'T.C. İnkılap Tarihi ve Atatürkçülük',
        'İngilizce',
      ];
    }
    // Lise (9-12. sınıf) - YKS kapsamındaki dersler
    else if ([EducationLevel.grade9, EducationLevel.grade10, EducationLevel.grade11, EducationLevel.grade12].contains(_selectedEducationLevel)) {
      return [
        'Matematik',
        'Türk Dili ve Edebiyatı',
        'Fizik',
        'Kimya',
        'Biyoloji',
        'Tarih',
        'Coğrafya',
        'Felsefe',
        'İngilizce',
      ];
    }
    // Mezun - YKS kapsamındaki tüm dersler
    else if (_selectedEducationLevel == EducationLevel.graduate) {
      return [
        'Matematik',
        'Türk Dili ve Edebiyatı',
        'Fizik',
        'Kimya',
        'Biyoloji',
        'Tarih',
        'Coğrafya',
        'Felsefe',
        'İngilizce',
      ];
    }
    else {
      return [];
    }
  }

  final List<String> _weekDays = [
    'Pazartesi',
    'Salı',
    'Çarşamba',
    'Perşembe',
    'Cuma',
    'Cumartesi',
    'Pazar',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _selectBirthDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = DateTime(now.year - 12, now.month, now.day); // 12 yaş varsayılan
    final DateTime firstDate = DateTime(now.year - 25, 1, 1); // 25 yaş max
    final DateTime lastDate = DateTime(now.year - 8, 12, 31); // 8 yaş min

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      helpText: 'Doğum Tarihi Seçin',
      cancelText: 'İptal',
      confirmText: 'Tamam',
      fieldLabelText: 'Doğum Tarihi',
      fieldHintText: 'gg/aa/yyyy',
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kayıt Ol'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: LinearProgressIndicator(
              value: (_currentPage + 1) / 5,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          
          // Page content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page;
                });
              },
              children: [
                _buildPersonalInfoPage(),
                _buildEducationPage(),
                _buildPreferencesPage(),
                _buildSchedulePage(),
                _buildGoalsPage(),
              ],
            ),
          ),
          
          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentPage > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: const Text('Geri'),
                    ),
                  ),
                if (_currentPage > 0) const SizedBox(width: 16),

                // Program sayfasında "Atla" butonu ekle
                if (_currentPage == 3)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey[400]!),
                      ),
                      child: const Text(
                        'Atla',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                  ),
                if (_currentPage == 3) const SizedBox(width: 16),

                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleNext,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(_currentPage == 4 ? 'Tamamla' : 'İleri'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Kişisel Bilgiler',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Seni daha iyi tanımak için birkaç bilgi alalım',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 32),
            
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Ad Soyad',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Lütfen adınızı girin';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'E-posta',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Lütfen e-posta adresinizi girin';
                }
                if (!value.contains('@')) {
                  return 'Geçerli bir e-posta adresi girin';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            InkWell(
              onTap: () => _selectBirthDate(context),
              borderRadius: BorderRadius.circular(4),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Doğum Tarihi',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.cake),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _selectedBirthDate == null
                      ? 'Doğum tarihinizi seçin'
                      : '${_selectedBirthDate!.day.toString().padLeft(2, '0')}/${_selectedBirthDate!.month.toString().padLeft(2, '0')}/${_selectedBirthDate!.year}',
                  style: TextStyle(
                    fontSize: 16,
                    color: _selectedBirthDate == null ? Colors.grey[600] : Colors.black,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Eğitim Bilgileri',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Hangi sınıfta okuyorsun?',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 32),

          const Text(
            'Sınıf Seviyesi',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          ...EducationLevel.values.map((level) =>
            RadioListTile<EducationLevel>(
              title: Text(_getEducationLevelText(level)),
              value: level,
              groupValue: _selectedEducationLevel,
              onChanged: (value) {
                setState(() {
                  _selectedEducationLevel = value!;
                  // Eğitim seviyesi değiştiğinde seçilen dersleri temizle
                  _selectedSubjects.clear();
                });
              },
            ),
          ),


        ],
      ),
    );
  }

  Widget _buildPreferencesPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Çalışma Tercihleri',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Öğrenme stilin ve çalışma alışkanlıkların',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 32),

          const Text(
            'Öğrenme Stilin',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          ...StudyStyle.values.map((style) =>
            RadioListTile<StudyStyle>(
              title: Text(_getStudyStyleText(style)),
              subtitle: Text(_getStudyStyleDescription(style)),
              value: style,
              groupValue: _selectedStudyStyle,
              onChanged: (value) {
                setState(() {
                  _selectedStudyStyle = value!;
                });
              },
            ),
          ),

          const SizedBox(height: 24),

          const Text(
            'İlgilendiğin Dersler',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableSubjects.map((subject) =>
              FilterChip(
                label: Text(subject),
                selected: _selectedSubjects.contains(subject),
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedSubjects.add(subject);
                    } else {
                      _selectedSubjects.remove(subject);
                    }
                  });
                },
              ),
            ).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulePage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Haftalık Program (İsteğe Bağlı)',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Okul/kurs saatlerini ekleyerek boş vakitlerini belirleyebiliriz. Bu adımı atlayıp daha sonra da ekleyebilirsin.',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),

          // Bilgilendirme mesajı
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[700]),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bu adım isteğe bağlıdır',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Programını şimdi eklemek istemiyorsan "Atla" butonunu kullanabilirsin. Daha sonra ana menüden ekleyebilirsin.',
                        style: TextStyle(
                          color: Colors.blue[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Haftalık program listesi
          if (_scheduleEntries.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Henüz program eklenmedi',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Okul saatlerin veya kurs programın varsa ekleyebilirsin',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            )
          else
            _buildScheduleList(),

          const SizedBox(height: 24),

          // Program ekleme butonu
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showAddScheduleDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Program Ekle'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.all(16),
                side: BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          if (_scheduleEntries.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.green),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Harika! Programın eklendi. Bu bilgileri kullanarak sana en uygun çalışma saatlerini önerebiliriz.',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildScheduleList() {
    // Programları başlığa göre gruplandır
    final Map<String, List<ScheduleEntry>> groupedEntries = {};
    for (final entry in _scheduleEntries) {
      final key = '${entry.title}|${entry.startTime.displayTime}-${entry.endTime.displayTime}';
      if (!groupedEntries.containsKey(key)) {
        groupedEntries[key] = [];
      }
      groupedEntries[key]!.add(entry);
    }

    return Column(
      children: groupedEntries.entries.map((group) {
        final entries = group.value;
        final firstEntry = entries.first;

        // Günleri sırala
        entries.sort((a, b) => a.dayOfWeek.index.compareTo(b.dayOfWeek.index));

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          firstEntry.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        if (firstEntry.description != null)
                          Text(
                            firstEntry.description!,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        const SizedBox(height: 8),
                        Text(
                          '${firstEntry.startTime.displayTime} - ${firstEntry.endTime.displayTime}',
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () {
                      setState(() {
                        // Bu grubun tüm girişlerini sil
                        for (final entry in entries) {
                          _scheduleEntries.remove(entry);
                        }
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: entries.map((entry) => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    entry.dayOfWeek.shortName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )).toList(),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGoalsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Hedefler ve Program',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Çalışma hedeflerin ve programın',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 32),

          Text(
            'Günlük Çalışma Hedefi: ${_dailyStudyGoal} dakika',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          Slider(
            value: _dailyStudyGoal.toDouble(),
            min: 30,
            max: 300,
            divisions: 18,
            label: '${_dailyStudyGoal} dk',
            onChanged: (value) {
              setState(() {
                _dailyStudyGoal = value.round();
              });
            },
          ),

          const SizedBox(height: 24),

          const Text(
            'Çalışma Günleri',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _weekDays.map((day) =>
              FilterChip(
                label: Text(day),
                selected: _selectedStudyDays.contains(day),
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedStudyDays.add(day);
                    } else {
                      _selectedStudyDays.remove(day);
                    }
                  });
                },
              ),
            ).toList(),
          ),

          const SizedBox(height: 32),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.check_circle,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                const Text(
                  'Hazırsın!',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Kişiselleştirilmiş çalışma deneyimin başlamak üzere.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleNext() async {
    if (_currentPage < 4) {
      if (_currentPage == 0) {
        if (!_formKey.currentState!.validate()) {
          return;
        }
        if (_selectedBirthDate == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Lütfen doğum tarihinizi seçin'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Son sayfa - kayıt tamamla
      await _completeRegistration();
    }
  }

  Future<void> _completeRegistration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userProvider = context.read<UserProvider>();

      if (_selectedBirthDate == null) {
        throw Exception('Lütfen doğum tarihinizi seçin');
      }

      await userProvider.createUser(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        birthDate: _selectedBirthDate!,
        userType: widget.userType,
        educationLevel: _selectedEducationLevel,
        subjects: _selectedSubjects,
        preferredStudyStyle: _selectedStudyStyle,
        dailyStudyGoalMinutes: _dailyStudyGoal,
        studyDays: _selectedStudyDays,
        weeklySchedule: _scheduleEntries.isNotEmpty
            ? WeeklySchedule(entries: _scheduleEntries)
            : null,
      );

      if (mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Kayıt sırasında hata oluştu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getEducationLevelText(EducationLevel level) {
    switch (level) {
      case EducationLevel.grade5:
        return '5. Sınıf (Ortaokul)';
      case EducationLevel.grade6:
        return '6. Sınıf (Ortaokul)';
      case EducationLevel.grade7:
        return '7. Sınıf (Ortaokul)';
      case EducationLevel.grade8:
        return '8. Sınıf (Ortaokul)';
      case EducationLevel.grade9:
        return '9. Sınıf (Lise)';
      case EducationLevel.grade10:
        return '10. Sınıf (Lise)';
      case EducationLevel.grade11:
        return '11. Sınıf (Lise)';
      case EducationLevel.grade12:
        return '12. Sınıf (Lise)';
      case EducationLevel.graduate:
        return 'Mezun (YKS Hazırlık)';
    }
  }

  String _getStudyStyleText(StudyStyle style) {
    switch (style) {
      case StudyStyle.visual:
        return 'Görsel';
      case StudyStyle.auditory:
        return 'İşitsel';
      case StudyStyle.kinesthetic:
        return 'Kinestetik';
      case StudyStyle.readingWriting:
        return 'Okuma/Yazma';
    }
  }

  String _getStudyStyleDescription(StudyStyle style) {
    switch (style) {
      case StudyStyle.visual:
        return 'Diyagramlar, renkler ve görseller';
      case StudyStyle.auditory:
        return 'Sesli okuma ve müzik';
      case StudyStyle.kinesthetic:
        return 'Hareket ve pratik uygulamalar';
      case StudyStyle.readingWriting:
        return 'Not alma ve yazma';
    }
  }

  void _showAddScheduleDialog(BuildContext context) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    List<DayOfWeek> selectedDays = [];
    TimeOfDay startTime = const TimeOfDay(hour: 9, minute: 0);
    TimeOfDay endTime = const TimeOfDay(hour: 10, minute: 0);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Okul/Kurs Programı Ekle'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Başlık',
                    hintText: 'Örn: Matematik Dersi, İngilizce Kursu',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Açıklama (Opsiyonel)',
                    hintText: 'Örn: Sınıf 9A, Öğretmen: Ahmet Bey',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Günler',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        children: DayOfWeek.values.map((day) =>
                          CheckboxListTile(
                            title: Text(day.displayName),
                            value: selectedDays.contains(day),
                            onChanged: (bool? value) {
                              setDialogState(() {
                                if (value == true) {
                                  selectedDays.add(day);
                                } else {
                                  selectedDays.remove(day);
                                }
                              });
                            },
                            dense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ).toList(),
                      ),
                    ),
                    if (selectedDays.isEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          'En az bir gün seçmelisiniz',
                          style: TextStyle(
                            color: Colors.red[600],
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: startTime,
                          );
                          if (time != null) {
                            setDialogState(() {
                              startTime = time;
                            });
                          }
                        },
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Başlangıç',
                            border: OutlineInputBorder(),
                          ),
                          child: Text(startTime.format(context)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: endTime,
                          );
                          if (time != null) {
                            setDialogState(() {
                              endTime = time;
                            });
                          }
                        },
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Bitiş',
                            border: OutlineInputBorder(),
                          ),
                          child: Text(endTime.format(context)),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('İptal'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.trim().isNotEmpty && selectedDays.isNotEmpty) {
                  // Her seçilen gün için ayrı entry oluştur
                  final newEntries = selectedDays.map((day) => ScheduleEntry(
                    id: const Uuid().v4(),
                    dayOfWeek: day,
                    startTime: TimeSlot(hour: startTime.hour, minute: startTime.minute),
                    endTime: TimeSlot(hour: endTime.hour, minute: endTime.minute),
                    title: titleController.text.trim(),
                    description: descriptionController.text.trim().isEmpty
                        ? null
                        : descriptionController.text.trim(),
                  )).toList();

                  setState(() {
                    _scheduleEntries.addAll(newEntries);
                  });

                  Navigator.pop(context);
                }
              },
              child: const Text('Ekle'),
            ),
          ],
        ),
      ),
    );
  }
}
